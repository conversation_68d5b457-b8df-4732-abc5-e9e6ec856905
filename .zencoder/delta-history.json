{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart", "baseContent": "import 'package:dropx/src/screens/stores/models/store.model.dart';\nimport 'package:dropx/src/screens/stores/repositories/store.repository.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoresController extends BaseVM {\n  final StoresRepository storesRepo;\n\n  StoresController({\n    required this.storesRepo,\n  });\n\n  // * Get Stores\n  Future<List<StoreModel>> getStores() async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.getStores();\n      },\n    );\n  }\n\n  // * Add Stores\n  Future<void> addStores({\n    required Map<String, dynamic> data,\n  }) async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.addStores(data: data);\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756912559704}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle", "baseContent": "pluginManagement {\n    def flutterSdkPath = {\n        def properties = new Properties()\n        file(\"local.properties\").withInputStream { properties.load(it) }\n        def flutterSdkPath = properties.getProperty(\"flutter.sdk\")\n        assert flutterSdkPath != null, \"flutter.sdk not set in local.properties\"\n        return flutterSdkPath\n    }()\n\n    includeBuild(\"$flutterSdkPath/packages/flutter_tools/gradle\")\n\n    repositories {\n        google()\n        mavenCentral()\n        gradlePluginPortal()\n    }\n}\n\nplugins {\n    id \"dev.flutter.flutter-plugin-loader\" version \"1.0.0\"\n    id \"com.android.application\" version \"8.6.0\" apply false\n    id \"org.jetbrains.kotlin.android\" version \"2.1.0\" apply false\n}\n\ninclude \":app\"\n", "baseTimestamp": 1756912592007}, "/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air dropx % cd packages\nxrgouda@Amrs-MacBook-Air packages % cd x\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1756912714521, "deltas": [{"timestamp": 1756912723747, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "xrgouda@Amrs-MacBook-Air packages % cd x"}, {"type": "DELETE", "lineNumber": 2, "oldContent": ""}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "DELETE", "lineNumber": 4, "oldContent": ""}, {"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "INSERT", "lineNumber": 1, "content": "xrgouda@Amrs-MacBook-Air packages % cd xr_helper"}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air xr_helper % flutter pub upgrade --major-versions"}, {"type": "INSERT", "lineNumber": 3, "content": "Resolving dependencies... (1.2s)"}, {"type": "INSERT", "lineNumber": 4, "content": "Changed 6 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 5, "content": "  intl: ^0.19.0 -> ^0.20.2"}, {"type": "INSERT", "lineNumber": 6, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 7, "content": "  firebase_messaging: ^15.1.3 -> ^16.0.1"}, {"type": "INSERT", "lineNumber": 8, "content": "  flutter_hooks: ^0.20.3 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 9, "content": "  flutter_form_builder: ^9.3.0 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 10, "content": "  flutter_lints: ^3.0.1 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 11, "content": "Resolving dependencies... "}, {"type": "INSERT", "lineNumber": 12, "content": "Downloading packages... (1.1s)"}, {"type": "INSERT", "lineNumber": 13, "content": "> _flutterfire_internals 1.3.61 (was 1.3.59)"}, {"type": "INSERT", "lineNumber": 14, "content": "  characters 1.4.0 (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 15, "content": "> firebase_core 4.1.0 (was 3.15.2)"}, {"type": "INSERT", "lineNumber": 16, "content": "> firebase_core_web 3.1.0 (was 2.24.1)"}, {"type": "INSERT", "lineNumber": 17, "content": "> firebase_messaging 16.0.1 (was 15.2.10)"}, {"type": "INSERT", "lineNumber": 18, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.6.10)"}, {"type": "INSERT", "lineNumber": 19, "content": "> firebase_messaging_web 4.0.1 (was 3.10.10)"}, {"type": "INSERT", "lineNumber": 20, "content": "> flutter_form_builder 10.2.0 (was 9.7.0)"}, {"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % "}]}, {"timestamp": 1756912736827, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % "}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Try `flutter pub outdated` for more information."}, {"type": "DELETE", "lineNumber": 21, "oldContent": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "DELETE", "lineNumber": 23, "oldContent": "Changed 11 dependencies!"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}]}, {"timestamp": 1756912742841, "changes": [{"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "> intl 0.20.2 (was 0.19.0)"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}, {"type": "MODIFY", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd ..                               ", "oldContent": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 32, "content": "xrgouda@Amrs-MacBook-Air packages % flutter pub upgrade --major-versions"}]}, {"timestamp": 1756912796516, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "MODIFY", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)", "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 33, "content": "Resolving dependencies... (2.6s)"}, {"type": "INSERT", "lineNumber": 34, "content": "Changed 7 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 35, "content": "  flutter_hooks: ^0.20.4 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 36, "content": "  flutter_form_builder: ^9.4.1 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 37, "content": "  permission_handler: ^11.3.1 -> ^12.0.1"}, {"type": "INSERT", "lineNumber": 38, "content": "  geolocator: ^13.0.1 -> ^14.0.2"}, {"type": "INSERT", "lineNumber": 39, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 40, "content": "  flutter_lints: ^4.0.0 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 41, "content": "  flutter_launcher_icons: ^0.13.1 -> ^0.14.4"}, {"type": "INSERT", "lineNumber": 42, "content": "Resolving dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`... "}, {"type": "INSERT", "lineNumber": 43, "content": "Downloading packages... (31.4s)"}, {"type": "INSERT", "lineNumber": 44, "content": "> _fe_analyzer_shared 88.0.0 (was 72.0.0)"}, {"type": "INSERT", "lineNumber": 45, "content": "> _flutterfire_internals 1.3.61 (was 1.3.44)"}, {"type": "INSERT", "lineNumber": 46, "content": "> analyzer 8.1.1 (was 6.7.0)"}, {"type": "INSERT", "lineNumber": 47, "content": "> another_flushbar 1.12.31 (was 1.12.30)"}, {"type": "INSERT", "lineNumber": 48, "content": "> any_link_preview 3.0.3 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 49, "content": "! archive 3.6.1 (overridden) (4.0.7 available)"}, {"type": "INSERT", "lineNumber": 50, "content": "> args 2.7.0 (was 2.6.0)"}, {"type": "INSERT", "lineNumber": 51, "content": "> async 2.13.0 (was 2.11.0)"}, {"type": "INSERT", "lineNumber": 52, "content": "> audio_waveforms 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 53, "content": "> boolean_selector 2.1.2 (was 2.1.1)"}, {"type": "INSERT", "lineNumber": 54, "content": "> build 3.1.0 (was 2.4.1) (4.0.0 available)"}, {"type": "INSERT", "lineNumber": 55, "content": "> build_config 1.2.0 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 56, "content": "> build_daemon 4.0.4 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 57, "content": "> build_resolvers 3.0.3 (was 2.4.2) (3.0.4 available)"}, {"type": "INSERT", "lineNumber": 58, "content": "> build_runner 2.7.1 (was 2.4.13) (2.7.2 available)"}, {"type": "INSERT", "lineNumber": 59, "content": "> build_runner_core 9.3.1 (was 7.3.2) (9.3.2 available)"}, {"type": "INSERT", "lineNumber": 60, "content": "> built_value 8.11.1 (was 8.9.2)"}, {"type": "INSERT", "lineNumber": 61, "content": "> carousel_slider 5.1.1 (was 5.0.0)"}, {"type": "INSERT", "lineNumber": 62, "content": "> characters 1.4.0 (was 1.3.0) (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 63, "content": "> chatview 2.5.0 (was 2.3.0)"}, {"type": "INSERT", "lineNumber": 64, "content": "+ chatview_utils 0.0.1"}, {"type": "INSERT", "lineNumber": 65, "content": "> checked_yaml 2.0.4 (was 2.0.3)"}, {"type": "INSERT", "lineNumber": 66, "content": "> cli_util 0.4.2 (was 0.4.1)"}, {"type": "INSERT", "lineNumber": 67, "content": "> clock 1.1.2 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 68, "content": "> code_builder 4.10.1 (was 4.10.0)"}, {"type": "INSERT", "lineNumber": 69, "content": "> collection 1.19.1 (was 1.18.0)"}, {"type": "INSERT", "lineNumber": 70, "content": "> convert 3.1.2 (was 3.1.1)"}, {"type": "INSERT", "lineNumber": 71, "content": "> crypto 3.0.6 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 72, "content": "> csslib 1.0.2 (was 1.0.0)"}, {"type": "INSERT", "lineNumber": 73, "content": "> dart_style 3.1.2 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 74, "content": "+ dbus 0.7.11"}, {"type": "INSERT", "lineNumber": 75, "content": "> emoji_picker_flutter 4.3.0 (was 3.1.0)"}, {"type": "INSERT", "lineNumber": 76, "content": "> equatable 2.0.7 (was 2.0.5)"}, {"type": "INSERT", "lineNumber": 77, "content": "> fake_async 1.3.3 (was 1.3.1)"}, {"type": "INSERT", "lineNumber": 78, "content": "> ffi 2.1.4 (was 2.1.3)"}, {"type": "INSERT", "lineNumber": 79, "content": "> file_selector_linux 0.9.3+2 (was 0.9.3)"}, {"type": "INSERT", "lineNumber": 80, "content": "> file_selector_macos 0.9.4+4 (was 0.9.4+2)"}, {"type": "INSERT", "lineNumber": 81, "content": "> file_selector_windows 0.9.3+4 (was 0.9.3+3)"}, {"type": "INSERT", "lineNumber": 82, "content": "> firebase_core 4.1.0 (was 3.6.0)"}, {"type": "INSERT", "lineNumber": 83, "content": "> firebase_core_platform_interface 6.0.0 (was 5.3.0)"}, {"type": "INSERT", "lineNumber": 84, "content": "> firebase_core_web 3.1.0 (was 2.18.1)"}, {"type": "INSERT", "lineNumber": 85, "content": "> firebase_messaging 16.0.1 (was 15.1.3)"}, {"type": "INSERT", "lineNumber": 86, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.5.46)"}, {"type": "INSERT", "lineNumber": 87, "content": "> firebase_messaging_web 4.0.1 (was 3.9.2)"}, {"type": "INSERT", "lineNumber": 88, "content": "> fixnum 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 89, "content": "> flutter_form_builder 10.2.0 (was 9.5.0)"}, {"type": "INSERT", "lineNumber": 90, "content": "> flutter_gen_core 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 91, "content": "> flutter_gen_runner 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 92, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 93, "content": "> flutter_launcher_icons 0.14.4 (was 0.13.1)"}, {"type": "INSERT", "lineNumber": 94, "content": "> flutter_lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 95, "content": "> flutter_native_splash 2.4.6 (was 2.4.1)"}, {"type": "INSERT", "lineNumber": 96, "content": "> flutter_plugin_android_lifecycle 2.0.30 (was 2.0.23)"}, {"type": "INSERT", "lineNumber": 97, "content": "> flutter_riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 98, "content": "> flutter_svg 2.2.0 (was 2.0.10+1)"}, {"type": "INSERT", "lineNumber": 99, "content": "> fluttertoast 8.2.12 (was 8.2.8)"}, {"type": "INSERT", "lineNumber": 100, "content": "> font_awesome_flutter 10.10.0 (was 10.7.0)"}, {"type": "INSERT", "lineNumber": 101, "content": "> form_builder_image_picker 4.3.1 (was 4.1.0)"}, {"type": "INSERT", "lineNumber": 102, "content": "> form_builder_validators 11.2.0 (was 11.0.0)"}, {"type": "INSERT", "lineNumber": 103, "content": "+ geoclue 0.1.1"}, {"type": "INSERT", "lineNumber": 104, "content": "> geolocator 14.0.2 (was 13.0.1)"}, {"type": "INSERT", "lineNumber": 105, "content": "> geolocator_android 5.0.2 (was 4.6.1)"}, {"type": "INSERT", "lineNumber": 106, "content": "> geolocator_apple 2.3.13 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 107, "content": "+ geolocator_linux 0.2.3"}, {"type": "INSERT", "lineNumber": 108, "content": "> geolocator_platform_interface 4.2.6 (was 4.2.4)"}, {"type": "INSERT", "lineNumber": 109, "content": "> geolocator_web 4.1.3 (was 4.1.1)"}, {"type": "INSERT", "lineNumber": 110, "content": "> geolocator_windows 0.2.5 (was 0.2.3)"}, {"type": "INSERT", "lineNumber": 111, "content": "> get 4.7.2 (was 4.6.6)"}, {"type": "INSERT", "lineNumber": 112, "content": "> glob 2.1.3 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 113, "content": "> google_fonts 6.3.1 (was 6.2.1)"}, {"type": "INSERT", "lineNumber": 114, "content": "> google_maps_flutter 2.13.1 (was 2.9.0)"}, {"type": "INSERT", "lineNumber": 115, "content": "> google_maps_flutter_android 2.18.2 (was 2.14.10)"}, {"type": "INSERT", "lineNumber": 116, "content": "> google_maps_flutter_ios 2.15.5 (was 2.13.1)"}, {"type": "INSERT", "lineNumber": 117, "content": "> google_maps_flutter_platform_interface 2.14.0 (was 2.9.5)"}, {"type": "INSERT", "lineNumber": 118, "content": "> google_maps_flutter_web 0.5.14 (was 0.5.10)"}, {"type": "INSERT", "lineNumber": 119, "content": "+ gsettings 0.2.8"}, {"type": "INSERT", "lineNumber": 120, "content": "> hooks_riverpod 2.6.1 (was 2.5.4)"}, {"type": "INSERT", "lineNumber": 121, "content": "> html 0.15.6 (was 0.15.4)"}, {"type": "INSERT", "lineNumber": 122, "content": "> http 1.5.0 (was 1.2.2)"}, {"type": "INSERT", "lineNumber": 123, "content": "> http_multi_server 3.2.2 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 124, "content": "> http_parser 4.1.2 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 125, "content": "> image 4.5.4 (was 4.2.0)"}, {"type": "INSERT", "lineNumber": 126, "content": "> image_picker 1.2.0 (was 1.1.2)"}, {"type": "INSERT", "lineNumber": 127, "content": "> image_picker_android 0.8.13+1 (was 0.8.12+17)"}, {"type": "INSERT", "lineNumber": 128, "content": "> image_picker_for_web 3.1.0 (was 3.0.6)"}, {"type": "INSERT", "lineNumber": 129, "content": "> image_picker_ios 0.8.13 (was 0.8.12+1)"}, {"type": "INSERT", "lineNumber": 130, "content": "> image_picker_linux 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 131, "content": "> image_picker_macos 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 132, "content": "> image_picker_platform_interface 2.11.0 (was 2.10.0)"}, {"type": "INSERT", "lineNumber": 133, "content": "> image_picker_windows 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 134, "content": "> image_size_getter 2.4.1 (was 2.2.0)"}, {"type": "INSERT", "lineNumber": 135, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 136, "content": "> io 1.0.5 (was 1.0.4)"}, {"type": "INSERT", "lineNumber": 137, "content": "> leak_tracker 11.0.1 (was 10.0.5)"}, {"type": "INSERT", "lineNumber": 138, "content": "> leak_tracker_flutter_testing 3.0.10 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 139, "content": "> leak_tracker_testing 3.0.2 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 140, "content": "> lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 141, "content": "> logger 2.6.1 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 142, "content": "> logging 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 143, "content": "> lottie 3.3.1 (was 3.1.3)"}, {"type": "INSERT", "lineNumber": 144, "content": "> matcher 0.12.17 (was 0.12.16+1)"}, {"type": "INSERT", "lineNumber": 145, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 146, "content": "> meta 1.16.0 (was 1.15.0) (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 147, "content": "> package_config 2.2.0 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 148, "content": "+ package_info_plus 8.3.1"}, {"type": "INSERT", "lineNumber": 149, "content": "+ package_info_plus_platform_interface 3.2.1"}, {"type": "INSERT", "lineNumber": 150, "content": "> path 1.9.1 (was 1.9.0)"}, {"type": "INSERT", "lineNumber": 151, "content": "> path_parsing 1.1.0 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 152, "content": "> path_provider 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 153, "content": "> path_provider_android 2.2.18 (was 2.2.12)"}, {"type": "INSERT", "lineNumber": 154, "content": "> path_provider_foundation 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 155, "content": "> permission_handler 12.0.1 (was 11.3.1)"}, {"type": "INSERT", "lineNumber": 156, "content": "> permission_handler_android 13.0.1 (was 12.0.13)"}, {"type": "INSERT", "lineNumber": 157, "content": "> permission_handler_apple 9.4.7 (was 9.4.5)"}, {"type": "INSERT", "lineNumber": 158, "content": "> permission_handler_html 0.1.3+5 (was 0.1.3+2)"}, {"type": "INSERT", "lineNumber": 159, "content": "> permission_handler_platform_interface 4.3.0 (was 4.2.3)"}, {"type": "INSERT", "lineNumber": 160, "content": "> petitparser 7.0.1 (was 6.0.2)"}, {"type": "INSERT", "lineNumber": 161, "content": "> platform 3.1.6 (was 3.1.5)"}, {"type": "INSERT", "lineNumber": 162, "content": "> provider 6.1.5+1 (was 6.1.2)"}, {"type": "INSERT", "lineNumber": 163, "content": "> pub_semver 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 164, "content": "> pubspec_parse 1.5.0 (was 1.3.0)"}, {"type": "INSERT", "lineNumber": 165, "content": "> riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 166, "content": "> shared_preferences 2.5.3 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 167, "content": "> shared_preferences_android 2.4.12 (was 2.3.4)"}, {"type": "INSERT", "lineNumber": 168, "content": "> shared_preferences_foundation 2.5.4 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 169, "content": "> shared_preferences_web 2.4.3 (was 2.4.2)"}, {"type": "INSERT", "lineNumber": 170, "content": "> shelf 1.4.2 (was 1.4.1)"}, {"type": "INSERT", "lineNumber": 171, "content": "> shelf_web_socket 3.0.0 (was 2.0.0)"}, {"type": "INSERT", "lineNumber": 172, "content": "< sky_engine 0.0.0 from sdk flutter (was 0.0.99 from sdk flutter)"}, {"type": "INSERT", "lineNumber": 173, "content": "> smooth_page_indicator 1.2.1 (was 1.2.0+3)"}, {"type": "INSERT", "lineNumber": 174, "content": "> source_span 1.10.1 (was 1.10.0)"}, {"type": "INSERT", "lineNumber": 175, "content": "> sqflite 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 176, "content": "> sqflite_android 2.4.2+2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 177, "content": "> sqflite_common 2.5.6 (was 2.5.4+5)"}, {"type": "INSERT", "lineNumber": 178, "content": "> sqflite_darwin 2.4.2 (was 2.4.1-1)"}, {"type": "INSERT", "lineNumber": 179, "content": "> stack_trace 1.12.1 (was 1.11.1)"}, {"type": "INSERT", "lineNumber": 180, "content": "> stream_channel 2.1.4 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 181, "content": "> stream_transform 2.1.1 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 182, "content": "> string_scanner 1.4.1 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 183, "content": "> string_validator 1.2.0 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 184, "content": "> synchronized 3.4.0 (was 3.3.0+3)"}, {"type": "INSERT", "lineNumber": 185, "content": "> term_glyph 1.2.2 (was 1.2.1)"}, {"type": "INSERT", "lineNumber": 186, "content": "> test_api 0.7.6 (was 0.7.2) (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 187, "content": "> time 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 188, "content": "> timing 1.0.2 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 189, "content": "> typed_data 1.4.0 (was 1.3.2)"}, {"type": "INSERT", "lineNumber": 190, "content": "> url_launcher 6.3.2 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 191, "content": "> url_launcher_android 6.3.18 (was 6.3.12)"}, {"type": "INSERT", "lineNumber": 192, "content": "> url_launcher_ios 6.3.4 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 193, "content": "> url_launcher_linux 3.2.1 (was 3.2.0)"}, {"type": "INSERT", "lineNumber": 194, "content": "> url_launcher_macos 3.2.3 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 195, "content": "> url_launcher_web 2.4.1 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 196, "content": "> url_launcher_windows 3.1.4 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 197, "content": "> vector_graphics 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 198, "content": "> vector_graphics_codec 1.1.13 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 199, "content": "> vector_graphics_compiler 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 200, "content": "> vector_math 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 201, "content": "> video_player 2.10.0 (was 2.9.2)"}, {"type": "INSERT", "lineNumber": 202, "content": "> video_player_android 2.8.13 (was 2.7.13)"}, {"type": "INSERT", "lineNumber": 203, "content": "> video_player_avfoundation 2.8.4 (was 2.6.2)"}, {"type": "INSERT", "lineNumber": 204, "content": "> video_player_platform_interface 6.4.0 (was 6.2.3)"}, {"type": "INSERT", "lineNumber": 205, "content": "> video_player_web 2.4.0 (was 2.3.2)"}, {"type": "INSERT", "lineNumber": 206, "content": "> vm_service 15.0.2 (was 14.2.5)"}, {"type": "INSERT", "lineNumber": 207, "content": "> watcher 1.1.3 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 208, "content": "> web 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 209, "content": "> web_socket 1.0.1 (was 0.1.6)"}, {"type": "INSERT", "lineNumber": 210, "content": "> web_socket_channel 3.0.3 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 211, "content": "! win32 5.14.0 (overridden)"}, {"type": "INSERT", "lineNumber": 212, "content": "> xml 6.6.1 (was 6.5.0)"}, {"type": "INSERT", "lineNumber": 213, "content": "> yaml 3.1.3 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 214, "content": "These packages are no longer being depended on:"}, {"type": "INSERT", "lineNumber": 215, "content": "- _macros 0.3.2 from sdk dart"}, {"type": "INSERT", "lineNumber": 216, "content": "- js 0.7.1"}, {"type": "INSERT", "lineNumber": 217, "content": "- macros 0.1.2-main.4"}, {"type": "INSERT", "lineNumber": 218, "content": "- timeago 3.7.0"}, {"type": "INSERT", "lineNumber": 219, "content": "Changed 171 dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`!"}, {"type": "INSERT", "lineNumber": 220, "content": "9 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 221, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 222, "content": "xrgouda@Amrs-MacBook-Air packages % "}]}, {"timestamp": 1756978368605, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air dropx % cd packages"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "xrgouda@Amrs-MacBook-Air packages % cd xr_helper"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % flutter pub upgrade --major-versions"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "Resolving dependencies... (1.2s)"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "Changed 6 constraints in pubspec.yaml:"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  intl: ^0.19.0 -> ^0.20.2"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  firebase_messaging: ^15.1.3 -> ^16.0.1"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  flutter_hooks: ^0.20.3 -> ^0.21.3+1"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  flutter_form_builder: ^9.3.0 -> ^10.2.0"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  flutter_lints: ^3.0.1 -> ^6.0.0"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "Resolving dependencies... "}, {"type": "DELETE", "lineNumber": 12, "oldContent": "Downloading packages... (1.1s)"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "> _flutterfire_internals 1.3.61 (was 1.3.59)"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  characters 1.4.0 (1.4.1 available)"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "> firebase_core 4.1.0 (was 3.15.2)"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "> firebase_core_web 3.1.0 (was 2.24.1)"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "> firebase_messaging 16.0.1 (was 15.2.10)"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "> firebase_messaging_platform_interface 4.7.1 (was 4.6.10)"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "> firebase_messaging_web 4.0.1 (was 3.10.10)"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "> flutter_form_builder 10.2.0 (was 9.7.0)"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "> lints 6.0.0 (was 3.0.0)"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "Changed 11 dependencies!"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "DELETE", "lineNumber": 30, "oldContent": "Try `flutter pub outdated` for more information."}, {"type": "DELETE", "lineNumber": 31, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % cd ..                               "}, {"type": "DELETE", "lineNumber": 32, "oldContent": "xrgouda@Amrs-MacBook-Air packages % flutter pub upgrade --major-versions"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "Resolving dependencies... (2.6s)"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "Changed 7 constraints in pubspec.yaml:"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "Try `flutter pub outdated` for more information."}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  flutter_hooks: ^0.20.4 -> ^0.21.3+1"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "9 packages have newer versions incompatible with dependency constraints."}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  flutter_form_builder: ^9.4.1 -> ^10.2.0"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "Changed 171 dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`!"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  permission_handler: ^11.3.1 -> ^12.0.1"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "- timeago 3.7.0"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  geolocator: ^13.0.1 -> ^14.0.2"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "- macros 0.1.2-main.4"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "- js 0.7.1"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  flutter_lints: ^4.0.0 -> ^6.0.0"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "- _macros 0.3.2 from sdk dart"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  flutter_launcher_icons: ^0.13.1 -> ^0.14.4"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "These packages are no longer being depended on:"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "Resolving dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`... "}, {"type": "DELETE", "lineNumber": 52, "oldContent": "> yaml 3.1.3 (was 3.1.2)"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "Downloading packages... (31.4s)"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "> xml 6.6.1 (was 6.5.0)"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "> _fe_analyzer_shared 88.0.0 (was 72.0.0)"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "! win32 5.14.0 (overridden)"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "> _flutterfire_internals 1.3.61 (was 1.3.44)"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "> web_socket_channel 3.0.3 (was 3.0.1)"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "> analyzer 8.1.1 (was 6.7.0)"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "> web_socket 1.0.1 (was 0.1.6)"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "> another_flushbar 1.12.31 (was 1.12.30)"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "> web 1.1.1 (was 1.1.0)"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "> any_link_preview 3.0.3 (was 3.0.2)"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "> watcher 1.1.3 (was 1.1.0)"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "! archive 3.6.1 (overridden) (4.0.7 available)"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "> vm_service 15.0.2 (was 14.2.5)"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "> args 2.7.0 (was 2.6.0)"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "> video_player_web 2.4.0 (was 2.3.2)"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "> async 2.13.0 (was 2.11.0)"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "> video_player_platform_interface 6.4.0 (was 6.2.3)"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "> audio_waveforms 1.3.0 (was 1.2.0)"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "> video_player_avfoundation 2.8.4 (was 2.6.2)"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "> boolean_selector 2.1.2 (was 2.1.1)"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "> video_player_android 2.8.13 (was 2.7.13)"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "> build 3.1.0 (was 2.4.1) (4.0.0 available)"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "> video_player 2.10.0 (was 2.9.2)"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "> build_config 1.2.0 (was 1.1.1)"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "> vector_math 2.2.0 (was 2.1.4)"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "> build_daemon 4.0.4 (was 4.0.2)"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "> vector_graphics_compiler 1.1.19 (was 1.1.11+1)"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "> build_resolvers 3.0.3 (was 2.4.2) (3.0.4 available)"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "> vector_graphics_codec 1.1.13 (was 1.1.11+1)"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "> build_runner 2.7.1 (was 2.4.13) (2.7.2 available)"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "> vector_graphics 1.1.19 (was 1.1.11+1)"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "> build_runner_core 9.3.1 (was 7.3.2) (9.3.2 available)"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "> url_launcher_windows 3.1.4 (was 3.1.2)"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "> built_value 8.11.1 (was 8.9.2)"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "> url_launcher_web 2.4.1 (was 2.3.3)"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "> carousel_slider 5.1.1 (was 5.0.0)"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "> url_launcher_macos 3.2.3 (was 3.2.1)"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "> characters 1.4.0 (was 1.3.0) (1.4.1 available)"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "> url_launcher_linux 3.2.1 (was 3.2.0)"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "> chatview 2.5.0 (was 2.3.0)"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "> url_launcher_ios 6.3.4 (was 6.3.1)"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "+ chatview_utils 0.0.1"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "> url_launcher_android 6.3.18 (was 6.3.12)"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "> checked_yaml 2.0.4 (was 2.0.3)"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "> url_launcher 6.3.2 (was 6.3.1)"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "> cli_util 0.4.2 (was 0.4.1)"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "> typed_data 1.4.0 (was 1.3.2)"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "> clock 1.1.2 (was 1.1.1)"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "> timing 1.0.2 (was 1.0.1)"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "> code_builder 4.10.1 (was 4.10.0)"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "> time 2.1.5 (was 2.1.4)"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "> collection 1.19.1 (was 1.18.0)"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "> test_api 0.7.6 (was 0.7.2) (0.7.7 available)"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "> convert 3.1.2 (was 3.1.1)"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "> term_glyph 1.2.2 (was 1.2.1)"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "> crypto 3.0.6 (was 3.0.5)"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "> synchronized 3.4.0 (was 3.3.0+3)"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "> csslib 1.0.2 (was 1.0.0)"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "> string_validator 1.2.0 (was 1.1.0)"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "> dart_style 3.1.2 (was 2.3.7)"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "> string_scanner 1.4.1 (was 1.2.0)"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "+ dbus 0.7.11"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "> stream_transform 2.1.1 (was 2.1.0)"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "> emoji_picker_flutter 4.3.0 (was 3.1.0)"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "> stream_channel 2.1.4 (was 2.1.2)"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "> equatable 2.0.7 (was 2.0.5)"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "> stack_trace 1.12.1 (was 1.11.1)"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "> fake_async 1.3.3 (was 1.3.1)"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "> sqflite_darwin 2.4.2 (was 2.4.1-1)"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "> ffi 2.1.4 (was 2.1.3)"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "> sqflite_common 2.5.6 (was 2.5.4+5)"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "> file_selector_linux 0.9.3+2 (was 0.9.3)"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "> sqflite_android 2.4.2+2 (was 2.4.0)"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "> file_selector_macos 0.9.4+4 (was 0.9.4+2)"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "> sqflite 2.4.2 (was 2.4.0)"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "> file_selector_windows 0.9.3+4 (was 0.9.3+3)"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "> source_span 1.10.1 (was 1.10.0)"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "> firebase_core 4.1.0 (was 3.6.0)"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "> smooth_page_indicator 1.2.1 (was 1.2.0+3)"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "> firebase_core_platform_interface 6.0.0 (was 5.3.0)"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "< sky_engine 0.0.0 from sdk flutter (was 0.0.99 from sdk flutter)"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "> firebase_core_web 3.1.0 (was 2.18.1)"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "> shelf_web_socket 3.0.0 (was 2.0.0)"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "> firebase_messaging 16.0.1 (was 15.1.3)"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "> shelf 1.4.2 (was 1.4.1)"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "> firebase_messaging_platform_interface 4.7.1 (was 4.5.46)"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "> shared_preferences_web 2.4.3 (was 2.4.2)"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "> firebase_messaging_web 4.0.1 (was 3.9.2)"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "> shared_preferences_foundation 2.5.4 (was 2.5.3)"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "> fixnum 1.1.1 (was 1.1.0)"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "> shared_preferences_android 2.4.12 (was 2.3.4)"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "> flutter_form_builder 10.2.0 (was 9.5.0)"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "> shared_preferences 2.5.3 (was 2.3.3)"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "> flutter_gen_core 5.11.0 (was 5.8.0)"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "> riverpod 2.6.1 (was 2.5.3)"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "> flutter_gen_runner 5.11.0 (was 5.8.0)"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "> pubspec_parse 1.5.0 (was 1.3.0)"}, {"type": "DELETE", "lineNumber": 151, "oldContent": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "> pub_semver 2.2.0 (was 2.1.4)"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "> flutter_launcher_icons 0.14.4 (was 0.13.1)"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "> provider 6.1.5+1 (was 6.1.2)"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "> flutter_lints 6.0.0 (was 4.0.0)"}, {"type": "DELETE", "lineNumber": 156, "oldContent": "> platform 3.1.6 (was 3.1.5)"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "> flutter_native_splash 2.4.6 (was 2.4.1)"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "> petitparser 7.0.1 (was 6.0.2)"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "> flutter_plugin_android_lifecycle 2.0.30 (was 2.0.23)"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "> permission_handler_platform_interface 4.3.0 (was 4.2.3)"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "> flutter_riverpod 2.6.1 (was 2.5.3)"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "> permission_handler_html 0.1.3+5 (was 0.1.3+2)"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "> flutter_svg 2.2.0 (was 2.0.10+1)"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "> permission_handler_apple 9.4.7 (was 9.4.5)"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "> fluttertoast 8.2.12 (was 8.2.8)"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "> permission_handler_android 13.0.1 (was 12.0.13)"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "> font_awesome_flutter 10.10.0 (was 10.7.0)"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "> permission_handler 12.0.1 (was 11.3.1)"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "> form_builder_image_picker 4.3.1 (was 4.1.0)"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "> path_provider_foundation 2.4.2 (was 2.4.0)"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "> form_builder_validators 11.2.0 (was 11.0.0)"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "> path_provider_android 2.2.18 (was 2.2.12)"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "+ geoclue 0.1.1"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "> path_provider 2.1.5 (was 2.1.4)"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "> geolocator 14.0.2 (was 13.0.1)"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "> path_parsing 1.1.0 (was 1.0.1)"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "> geolocator_android 5.0.2 (was 4.6.1)"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "> path 1.9.1 (was 1.9.0)"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "> geolocator_apple 2.3.13 (was 2.3.7)"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "+ package_info_plus_platform_interface 3.2.1"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "+ geolocator_linux 0.2.3"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "+ package_info_plus 8.3.1"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "> geolocator_platform_interface 4.2.6 (was 4.2.4)"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "> package_config 2.2.0 (was 2.1.0)"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "> geolocator_web 4.1.3 (was 4.1.1)"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "> meta 1.16.0 (was 1.15.0) (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 187, "oldContent": "> geolocator_windows 0.2.5 (was 0.2.3)"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "> get 4.7.2 (was 4.6.6)"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "> matcher 0.12.17 (was 0.12.16+1)"}, {"type": "DELETE", "lineNumber": 191, "oldContent": "> glob 2.1.3 (was 2.1.2)"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "> lottie 3.3.1 (was 3.1.3)"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "> google_fonts 6.3.1 (was 6.2.1)"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "> logging 1.3.0 (was 1.2.0)"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "> google_maps_flutter 2.13.1 (was 2.9.0)"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "> logger 2.6.1 (was 2.4.0)"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "> google_maps_flutter_android 2.18.2 (was 2.14.10)"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "> lints 6.0.0 (was 4.0.0)"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "> google_maps_flutter_ios 2.15.5 (was 2.13.1)"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "> leak_tracker_testing 3.0.2 (was 3.0.1)"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "> google_maps_flutter_platform_interface 2.14.0 (was 2.9.5)"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "> leak_tracker_flutter_testing 3.0.10 (was 3.0.5)"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "> google_maps_flutter_web 0.5.14 (was 0.5.10)"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "> leak_tracker 11.0.1 (was 10.0.5)"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "+ gsettings 0.2.8"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "> io 1.0.5 (was 1.0.4)"}, {"type": "DELETE", "lineNumber": 207, "oldContent": "> hooks_riverpod 2.6.1 (was 2.5.4)"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "> intl 0.20.2 (was 0.19.0)"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "> html 0.15.6 (was 0.15.4)"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "> image_size_getter 2.4.1 (was 2.2.0)"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "> http 1.5.0 (was 1.2.2)"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "> image_picker_windows 0.2.2 (was 0.2.1+1)"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "> http_multi_server 3.2.2 (was 3.2.1)"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "> image_picker_platform_interface 2.11.0 (was 2.10.0)"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "> http_parser 4.1.2 (was 4.0.2)"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "> image_picker_macos 0.2.2 (was 0.2.1+1)"}, {"type": "DELETE", "lineNumber": 217, "oldContent": "> image 4.5.4 (was 4.2.0)"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "> image_picker_linux 0.2.2 (was 0.2.1+1)"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "> image_picker 1.2.0 (was 1.1.2)"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "> image_picker_ios 0.8.13 (was 0.8.12+1)"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "> image_picker_android 0.8.13+1 (was 0.8.12+17)"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "> image_picker_for_web 3.1.0 (was 3.0.6)"}, {"type": "INSERT", "lineNumber": 1, "content": ""}, {"type": "INSERT", "lineNumber": 2, "content": ""}, {"type": "INSERT", "lineNumber": 3, "content": ""}, {"type": "INSERT", "lineNumber": 4, "content": ""}, {"type": "INSERT", "lineNumber": 5, "content": ""}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}]}, {"timestamp": 1756978370731, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air packages % dart run flutter_launcher_icons:main"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "xrgouda@Amrs-MacBook-Air packages % "}]}, {"timestamp": 1756978373648, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Could not find a file named \"pubspec.yaml\" in \"/Users/<USER>/Flutter-Projects/Ajory/dropx/packages\"."}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air packages % "}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}]}, {"timestamp": 1756978393834, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air packages % dart run flutter_launcher_icons:main"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Could not find a file named \"pubspec.yaml\" in \"/Users/<USER>/Flutter-Projects/Ajory/dropx/packages\"."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air dropx % dart run flutter_launcher_icons:main"}, {"type": "INSERT", "lineNumber": 1, "content": "Building package executable... (1.3s)"}, {"type": "INSERT", "lineNumber": 2, "content": "Built flutter_launcher_icons:main."}, {"type": "INSERT", "lineNumber": 3, "content": "This command is deprecated and replaced with \"flutter pub run flutter_launcher_icons\""}, {"type": "INSERT", "lineNumber": 4, "content": "  ════════════════════════════════════════════"}, {"type": "INSERT", "lineNumber": 5, "content": "     FLUTTER LAUNCHER ICONS (v0.14.4)                               "}, {"type": "INSERT", "lineNumber": 6, "content": "  ════════════════════════════════════════════"}, {"type": "INSERT", "lineNumber": 7, "content": "  "}, {"type": "INSERT", "lineNumber": 8, "content": "• Creating default icons Android"}, {"type": "INSERT", "lineNumber": 9, "content": "• Creating adaptive icons Android"}, {"type": "INSERT", "lineNumber": 10, "content": "• Overwriting the default Android launcher icon with a new icon"}, {"type": "INSERT", "lineNumber": 11, "content": "• Creating mipmap xml file Android"}, {"type": "INSERT", "lineNumber": 12, "content": "• Overwriting default iOS launcher icon with new icon"}, {"type": "INSERT", "lineNumber": 13, "content": "No platform provided"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "xrgouda@Amrs-MacBook-Air packages % "}, {"type": "INSERT", "lineNumber": 15, "content": "✓ Successfully generated launcher icons"}, {"type": "INSERT", "lineNumber": 16, "content": "xrgouda@Amrs-MacBook-Air dropx % dart run flutter_native_splash:create"}, {"type": "INSERT", "lineNumber": 17, "content": "Building package executable... (2.5s)"}, {"type": "INSERT", "lineNumber": 18, "content": "Built flutter_native_splash:create."}, {"type": "INSERT", "lineNumber": 19, "content": "[Android] Creating default splash images"}, {"type": "INSERT", "lineNumber": 20, "content": "[Android] Creating default android12splash images"}, {"type": "INSERT", "lineNumber": 21, "content": "[Android] Creating dark mode android12splash images"}, {"type": "INSERT", "lineNumber": 22, "content": "[Android] Updating launch background(s) with splash image path..."}, {"type": "INSERT", "lineNumber": 23, "content": "[Android]  - android/app/src/main/res/drawable/launch_background.xml"}, {"type": "INSERT", "lineNumber": 24, "content": "[Android]  - android/app/src/main/res/drawable-v21/launch_background.xml"}, {"type": "INSERT", "lineNumber": 25, "content": "[Android] Updating styles..."}, {"type": "INSERT", "lineNumber": 26, "content": "[Android]  - android/app/src/main/res/values-v31/styles.xml"}, {"type": "INSERT", "lineNumber": 27, "content": "[Android]  - android/app/src/main/res/values-night-v31/styles.xml"}, {"type": "INSERT", "lineNumber": 28, "content": "[Android]  - android/app/src/main/res/values/styles.xml"}, {"type": "INSERT", "lineNumber": 29, "content": "[Android]  - android/app/src/main/res/values-night/styles.xml"}, {"type": "INSERT", "lineNumber": 30, "content": "[iOS] Creating  images"}, {"type": "INSERT", "lineNumber": 31, "content": "[iOS] Updating ios/Runner/Info.plist for status bar hidden/visible"}, {"type": "INSERT", "lineNumber": 33, "content": "✅ Native splash complete."}, {"type": "INSERT", "lineNumber": 34, "content": "Now go finish building something awesome! 💪 You rock! 🤘🤩"}, {"type": "INSERT", "lineNumber": 35, "content": "Like the package? Please give it a 👍 here: https://pub.dev/packages/flutter_native_splash"}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "INSERT", "lineNumber": 37, "content": "xrgouda@Amrs-MacBook-Air dropx % "}]}, {"timestamp": 1757258429697, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air dropx % dart run flutter_launcher_icons:main"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Building package executable... (1.3s)"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "Built flutter_launcher_icons:main."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air dropx % "}, {"type": "DELETE", "lineNumber": 4, "oldContent": "This command is deprecated and replaced with \"flutter pub run flutter_launcher_icons\""}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  ════════════════════════════════════════════"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "     FLUTTER LAUNCHER ICONS (v0.14.4)                               "}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  ════════════════════════════════════════════"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  "}, {"type": "DELETE", "lineNumber": 11, "oldContent": "• Creating default icons Android"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "• Creating adaptive icons Android"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "• Overwriting the default Android launcher icon with a new icon"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "• Creating mipmap xml file Android"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "xrgouda@Amrs-MacBook-Air dropx % "}, {"type": "DELETE", "lineNumber": 16, "oldContent": "• Overwriting default iOS launcher icon with new icon"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "Like the package? Please give it a 👍 here: https://pub.dev/packages/flutter_native_splash"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "No platform provided"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Now go finish building something awesome! 💪 You rock! 🤘🤩"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "✅ Native splash complete."}, {"type": "DELETE", "lineNumber": 21, "oldContent": "✓ Successfully generated launcher icons"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "[iOS] Updating ios/Runner/Info.plist for status bar hidden/visible"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "xrgouda@Amrs-MacBook-Air dropx % dart run flutter_native_splash:create"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "[iOS] Creating  images"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "Building package executable... (2.5s)"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "[Android]  - android/app/src/main/res/values-night/styles.xml"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "Built flutter_native_splash:create."}, {"type": "DELETE", "lineNumber": 28, "oldContent": "[Android]  - android/app/src/main/res/values/styles.xml"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "[Android] Creating default splash images"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "[Android]  - android/app/src/main/res/values-night-v31/styles.xml"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "[Android] Creating default android12splash images"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "[Android]  - android/app/src/main/res/values-v31/styles.xml"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "[Android] Creating dark mode android12splash images"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "[Android] Updating styles..."}, {"type": "DELETE", "lineNumber": 35, "oldContent": "[Android] Updating launch background(s) with splash image path..."}, {"type": "DELETE", "lineNumber": 36, "oldContent": "[Android]  - android/app/src/main/res/drawable-v21/launch_background.xml"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "[Android]  - android/app/src/main/res/drawable/launch_background.xml"}, {"type": "INSERT", "lineNumber": 4, "content": ""}, {"type": "INSERT", "lineNumber": 5, "content": ""}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}]}, {"timestamp": 1757258468197, "changes": [{"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml", "baseContent": "name: xr_helper\ndescription: \"A new Flutter package project.\"\nversion: 0.0.1\nhomepage:\n\nenvironment:\n  sdk: '>=3.2.0 <4.0.0'\n  flutter: \">=1.17.0\"\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  cupertino_icons: ^1.0.6\n\n  #? URL Launcher (Make calls, send SMS, open URLs)\n  url_launcher: ^6.2.1\n\n  #? Local Storage\n  get_storage: ^2.1.1\n\n  #? Remote (HTTP Requests)\n  http: ^1.1.2\n\n  #? Sized Box (Height & Width)\n  gap: ^3.0.1\n\n  #? Intl (Date Formatting)\n  intl: ^0.20.2\n\n  #? Theme (Fonts)\n  google_fonts: ^6.1.0\n\n  #? Alerts\n  another_flushbar: ^1.12.30\n  fluttertoast: ^8.2.8\n\n  #? Firebase\n  firebase_core: ^4.1.0\n  firebase_messaging: ^16.0.1\n\n  #? Hooks\n  flutter_hooks: ^0.21.3+1\n\n  #? UI\n  cached_network_image:\n  shimmer: ^3.0.0\n  flutter_form_builder: ^10.2.0\n\n  #? Logger (Log messages with colors)\n  logger: ^2.0.2+1\n\n  #? State Management\n  provider: ^6.1.1\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n  flutter_lints: ^6.0.0\n\nflutter:\n", "baseTimestamp": 1756912724040}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml", "baseContent": "name: dropx\ndescription: \"DropX Application.\"\n\npublish_to: 'none'\n\nscripts:\n  build_runner: dart run build_runner build --delete-conflicting-outputs\n  launch_icons: flutter pub run flutter_launcher_icons:main\n  launch_splash: flutter pub run flutter_native_splash:create\n\nversion: 1.0.0+0\n\nenvironment:\n  sdk: '>=3.0.6 <4.0.0'\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  #? Localization\n  flutter_localizations:\n    sdk: flutter\n\n  # * Helper Package *\n  xr_helper:\n    path: packages/xr_helper\n\n  cupertino_icons: ^1.0.6\n\n  #? State Management\n  flutter_riverpod: ^2.4.9\n  riverpod: ^2.4.9\n  hooks_riverpod: ^2.4.9\n  flutter_hooks: ^0.21.3+1\n  equatable:\n\n  #? responsive\n  flutter_screenutil: ^5.9.3\n\n\n  #? Google Fonts\n  google_fonts:\n\n  #? Assets\n  lottie: ^3.0.0\n  flutter_svg:\n  smooth_page_indicator: ^1.2.0+3\n\n  #? form Builder\n  flutter_form_builder: ^10.2.0\n  form_builder_image_picker: ^4.1.0\n  form_builder_validators: ^11.0.0\n\n\n  #? Utils\n  no_context_navigation: ^3.0.0\n  fluttertoast: ^8.2.8\n  permission_handler: ^12.0.1\n\n  #? Location\n  google_maps_flutter: ^2.9.0\n  geolocator: ^14.0.2\n\n  #? Firebase\n  firebase_core: ^4.1.0\n\n  #? UI\n  loading_animation_widget: ^1.3.0\n  carousel_slider: ^5.0.0\n  multi_video_player: ^0.0.5\n  font_awesome_flutter: ^10.7.0\n  flutter_staggered_grid_view: ^0.7.0\n  chatview: ^2.3.0\n\ndependency_overrides:\n  archive: ^3.6.1\n  win32: ^5.5.4\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n\n  flutter_lints: ^6.0.0\n  flutter_launcher_icons: ^0.14.4\n  flutter_native_splash: ^2.3.9\n  build_runner:\n  flutter_gen_runner:\n\n#? dart run flutter_launcher_icons:main\nflutter_launcher_icons:\n  android: true\n  ios: true\n  remove_alpha_ios: true\n  image_path: \"assets/images/app_icon.png\"\n  adaptive_icon_background: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground_inset: 16\n\n# ? dart run flutter_native_splash:create\nflutter_native_splash:\n  android: true\n  ios: true\n  web: false\n  fullscreen: false\n  color: '#ffffff'\n  image: 'assets/images/app_icon.png'\n  android_12:\n    color: '#ffffff'\n    image: 'assets/images/app_icon.png'\n\nflutter:\n  uses-material-design: true\n  generate: true\n\n  assets:\n    - assets/images/\n#    - assets/animated/\n#    - assets/icons/\n\nflutter_intl:\n  enabled: true\n\n\nflutter_gen:\n  output: lib/generated", "baseTimestamp": 1756912796821, "deltas": [{"timestamp": 1756978345561, "changes": [{"type": "MODIFY", "lineNumber": 95, "content": "  adaptive_icon_background: \"assets/images/logo_symbol.png\"", "oldContent": "  adaptive_icon_background: \"assets/images/app_icon.png\""}]}, {"timestamp": 1756978351847, "changes": [{"type": "MODIFY", "lineNumber": 94, "content": "  image_path: \"assets/images/logo_symbol.png\"", "oldContent": "  image_path: \"assets/images/app_icon.png\""}, {"type": "MODIFY", "lineNumber": 96, "content": "  adaptive_icon_foreground: \"assets/images/logo_symbol.png\"", "oldContent": "  adaptive_icon_foreground: \"assets/images/app_icon.png\""}, {"type": "MODIFY", "lineNumber": 106, "content": "  image: 'assets/images/logo_symbol.png'", "oldContent": "  image: 'assets/images/app_icon.png'"}, {"type": "MODIFY", "lineNumber": 109, "content": "    image: 'assets/images/logo_symbol.png'", "oldContent": "    image: 'assets/images/app_icon.png'"}]}, {"timestamp": 1757268565891, "changes": [{"type": "INSERT", "lineNumber": 35, "content": "  pinput: ^5.0.2  "}]}, {"timestamp": 1757268568090, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "  pinput: ^5.0.2", "oldContent": "  pinput: ^5.0.2  "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml", "baseContent": "<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>\n\n    <application\n            android:label=\"DropX\"\n            android:name=\"${applicationName}\"\n            android:icon=\"@mipmap/ic_launcher\">\n\n\n        <meta-data\n                android:name=\"com.google.android.geo.API_KEY\"\n                android:value=\"AIzaSyCTUQH9tBTBxjAJSpDmDEVllVhWqmR0nR8\"/>\n\n        <activity\n                android:name=\".MainActivity\"\n                android:exported=\"true\"\n                android:launchMode=\"singleTop\"\n                android:taskAffinity=\"\"\n                android:theme=\"@style/LaunchTheme\"\n                android:configChanges=\"orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode\"\n                android:hardwareAccelerated=\"true\"\n                android:windowSoftInputMode=\"adjustResize\">\n\n\n            <!-- Specifies an Android theme to apply to this Activity as soon as\n                 the Android process has started. This theme is visible to the user\n                 while the Flutter UI initializes. After that, this theme continues\n                 to determine the Window background behind the Flutter UI. -->\n            <meta-data\n                    android:name=\"io.flutter.embedding.android.NormalTheme\"\n                    android:resource=\"@style/NormalTheme\"\n            />\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\"/>\n                <category android:name=\"android.intent.category.LAUNCHER\"/>\n            </intent-filter>\n        </activity>\n        <!-- Don't delete the meta-data below.\n             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->\n        <meta-data\n                android:name=\"flutterEmbedding\"\n                android:value=\"2\"/>\n    </application>\n    <!-- Required to query activities that can process text, see:\n         https://developer.android.com/training/package-visibility and\n         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.\n\n         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->\n    <queries>\n        <intent>\n            <action android:name=\"android.intent.action.PROCESS_TEXT\"/>\n            <data android:mimeType=\"text/plain\"/>\n        </intent>\n    </queries>\n</manifest>\n", "baseTimestamp": 1756913020326}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart", "baseContent": "import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;\nimport 'package:flutter_localizations/flutter_localizations.dart';\nimport 'package:dropx/generated/l10n.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConsts {\n  static const String appName = 'DropX';\n  static const Locale locale = Locale('en');\n\n  static const List<Locale> supportedLocales = [\n    locale,\n    Locale('ar'),\n  ];\n\n  static bool get isEnglish =>\n      GetStorageService.getData(key: LocalKeys.language) == 'en';\n\n  static const List<LocalizationsDelegate> localizationsDelegates = [\n    S.delegate,\n    GlobalMaterialLocalizations.delegate,\n    GlobalCupertinoLocalizations.delegate,\n    GlobalWidgetsLocalizations.delegate,\n  ];\n\n  //? Test Login\n  static const String testEmail = 'admin';\n  static const String testPass = 'test@123';\n}\n", "baseTimestamp": 1756913096385, "deltas": [{"timestamp": 1756968383891, "changes": [{"type": "MODIFY", "lineNumber": 11, "content": "    locale,", "oldContent": "    locale,"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass DoctorsListWidget extends StatelessWidget {\n  const DoctorsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.doctors, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const DoctorsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Column(\n                    children: [\n                      CircleAvatar(\n                          radius: 40.r,\n                          backgroundColor: ColorManager.lightGrey,\n                          child: const Padding(\n                              padding: EdgeInsets.all(AppSpaces.padding4),\n                              child: BaseCachedImage(\n                                'https://t4.ftcdn.net/jpg/02/60/04/09/360_F_260040900_oO6YW1sHTnKxby4GcjCvtypUCWjnQRg5.jpg',\n                                radius: AppRadius.radius100,\n                                height: 100,\n                                fit: BoxFit.cover,\n                              ))),\n                      AppGaps.gap4,\n                      Text(\n                        'Amr Gouda',\n                        style: AppTextStyles.labelLarge,\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930315842, "deltas": [{"timestamp": 1756930319151, "changes": [{"type": "DELETE", "lineNumber": 15, "oldContent": "        Row("}, {"type": "DELETE", "lineNumber": 16, "oldContent": "          children: ["}, {"type": "DELETE", "lineNumber": 17, "oldContent": "            Text(context.tr.doctors, style: AppTextStyles.title),"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "            const Spacer(),"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "            SeeAllButtonWidget("}, {"type": "DELETE", "lineNumber": 20, "oldContent": "              onPressed: () {"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "                const Doctors<PERSON><PERSON>en().navigate;"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "              },"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "            )"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          ],"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 15, "content": "     "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/home/<USER>/widgets/home_slider.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/animals_list.widget.dart';\nimport 'widgets/doctors_list.widget.dart';\nimport 'widgets/products_list.widget.dart';\n\nclass HomeScreen extends StatelessWidget {\n  const HomeScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(\n        backgroundColor: ColorManager.white,\n        surfaceTintColor: ColorManager.white,\n        centerTitle: false,\n        title: Text(\n          context.tr.welcomeWithName('Amr'),\n          style: AppTextStyles.title,\n        ),\n        leading: Padding(\n          padding: const EdgeInsets.all(AppSpaces.padding8),\n          child: CircleAvatar(\n            backgroundColor: ColorManager.lightPrimaryColor,\n            radius: 40.r,\n            child: ClipOval(\n              child: BaseCachedImage(\n                height: 80.h,\n                width: 80.w,\n                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',\n                fit: BoxFit.cover, // Ensures the image covers the circular area\n              ),\n            ),\n          ),\n        ),\n        actions: [\n          IconButton(\n              onPressed: () {},\n              icon: const CircleAvatar(\n                  backgroundColor: ColorManager.primaryColor,\n                  child: Icon(Icons.notifications))),\n        ],\n      ),\n      body: ListView(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        children: const [\n          AppGaps.gap16,\n\n          // * Home Slider\n          HomeSliderWidget(),\n\n          AppGaps.gap16,\n\n          // * Animals\n          AnimalsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Products\n          ProductsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Stores\n          HomeStoresListWidget(),\n\n          AppGaps.gap24,\n\n          // * Doctors\n          DoctorsListWidget(),\n\n          AppGaps.gap24,\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930331759, "deltas": [{"timestamp": 1757279249870, "changes": [{"type": "MODIFY", "lineNumber": 8, "content": "import 'package:dropx/src/screens/orders/models/order.model.dart';", "oldContent": "import 'widgets/animals_list.widget.dart';"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "        actions: ["}, {"type": "DELETE", "lineNumber": 44, "oldContent": "          IconButton("}, {"type": "INSERT", "lineNumber": 43, "content": "        backgroundColor: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 44, "content": "        surfaceTintColor: ColorManager.white,"}, {"type": "MODIFY", "lineNumber": 72, "content": "          ),", "oldContent": "          HomeStoresListWidget(),"}, {"type": "MODIFY", "lineNumber": 83, "content": "            ),", "oldContent": "            ),"}, {"type": "MODIFY", "lineNumber": 108, "content": "              fontFamily: GoogleFonts.cairo().fontFamily,", "oldContent": "              fontFamily: GoogleFonts.poppins().fontFamily,"}, {"type": "INSERT", "lineNumber": 115, "content": "          ),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "INSERT", "lineNumber": 118, "content": ""}, {"type": "INSERT", "lineNumber": 119, "content": "          // Tab Bar View"}, {"type": "INSERT", "lineNumber": 120, "content": "          Expanded("}, {"type": "INSERT", "lineNumber": 121, "content": "            child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 156, "content": "            ),"}, {"type": "INSERT", "lineNumber": 157, "content": "            AppGaps.gap16,"}, {"type": "INSERT", "lineNumber": 158, "content": "            Text("}, {"type": "INSERT", "lineNumber": 159, "content": "              emptyMessage,"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "            AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "            Text("}, {"type": "DELETE", "lineNumber": 166, "oldContent": "              emptyMessage,"}, {"type": "DELETE", "lineNumber": 171, "oldContent": ""}, {"type": "DELETE", "lineNumber": 172, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 173, "oldContent": ""}, {"type": "DELETE", "lineNumber": 174, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 171, "content": "      padding: EdgeInsets.only(bottom: 100.h),"}, {"type": "INSERT", "lineNumber": 172, "content": "      itemBuilder: (order, index) => ShipmentCardWidget("}, {"type": "INSERT", "lineNumber": 173, "content": "        order: order,"}, {"type": "INSERT", "lineNumber": 174, "content": "        onTap: () {"}, {"type": "MODIFY", "lineNumber": 177, "content": "      ),", "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 178, "content": "    );"}, {"type": "INSERT", "lineNumber": 179, "content": "  }"}, {"type": "INSERT", "lineNumber": 180, "content": "}"}, {"type": "INSERT", "lineNumber": 181, "content": ""}]}, {"timestamp": 1757279255959, "changes": [{"type": "MODIFY", "lineNumber": 32, "content": "  void dispose() {", "oldContent": "            child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "        actions: ["}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "    return Scaffold("}, {"type": "INSERT", "lineNumber": 42, "content": "      appBar: AppBar("}, {"type": "INSERT", "lineNumber": 82, "content": "              vertical: 16.h,"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "            ),"}, {"type": "INSERT", "lineNumber": 106, "content": "              fontFamily: GoogleFonts.cairo().fontFamily,"}, {"type": "DELETE", "lineNumber": 111, "oldContent": ""}, {"type": "DELETE", "lineNumber": 112, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 112, "content": "            tabs: ["}, {"type": "INSERT", "lineNumber": 113, "content": "              Tab(text: context.tr.myReceivedShipments),"}, {"type": "INSERT", "lineNumber": 114, "content": "              Tab(text: context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 115, "content": "            ],"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 117, "content": ""}, {"type": "DELETE", "lineNumber": 147, "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 148, "content": "    if (orders.isEmpty) {", "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 149, "content": "      return Center("}, {"type": "DELETE", "lineNumber": 157, "oldContent": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "DELETE", "lineNumber": 159, "oldContent": "                color: ColorManager.greyText,"}, {"type": "INSERT", "lineNumber": 161, "content": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "INSERT", "lineNumber": 162, "content": "                color: ColorManager.greyText,"}, {"type": "DELETE", "lineNumber": 169, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 170, "content": "    return BaseList<OrderModel>(", "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 171, "content": "      data: orders,"}, {"type": "DELETE", "lineNumber": 176, "oldContent": ""}, {"type": "INSERT", "lineNumber": 177, "content": "        },"}, {"type": "INSERT", "lineNumber": 181, "content": "}"}, {"type": "INSERT", "lineNumber": 182, "content": ""}]}, {"timestamp": 1757279265193, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "import 'package:flutter_screenutil/flutter_screenutil.dart';"}, {"type": "INSERT", "lineNumber": 3, "content": "import 'package:flutter_screenutil/flutter_screenutil.dart';"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "import 'package:dropx/src/core/theme/color_manager.dart';"}, {"type": "INSERT", "lineNumber": 15, "content": "  const HomeScreen({super.key});"}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 17, "content": "  @override", "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "            ),"}, {"type": "MODIFY", "lineNumber": 104, "content": "            ", "oldContent": ""}, {"type": "INSERT", "lineNumber": 105, "content": "            labelStyle: AppTextStyles.bodyMedium.copyWith("}, {"type": "INSERT", "lineNumber": 106, "content": "              fontWeight: FontWeight.w600,"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 108, "content": "            ),"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 147, "content": "", "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 148, "content": "  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "INSERT", "lineNumber": 162, "content": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "DELETE", "lineNumber": 169, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 170, "content": ""}, {"type": "DELETE", "lineNumber": 176, "oldContent": ""}, {"type": "INSERT", "lineNumber": 177, "content": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 180, "oldContent": ""}, {"type": "INSERT", "lineNumber": 181, "content": "  }"}, {"type": "INSERT", "lineNumber": 183, "content": ""}]}, {"timestamp": 1757279275538, "changes": [{"type": "DELETE", "lineNumber": 104, "oldContent": "            "}, {"type": "DELETE", "lineNumber": 105, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 104, "content": "            labelStyle: AppTextStyles.bodyMedium.copyWith("}, {"type": "MODIFY", "lineNumber": 145, "content": "  }", "oldContent": "}"}, {"type": "DELETE", "lineNumber": 180, "oldContent": ""}, {"type": "INSERT", "lineNumber": 179, "content": "    );"}, {"type": "INSERT", "lineNumber": 181, "content": "}"}]}, {"timestamp": 1757279315618, "changes": [{"type": "MODIFY", "lineNumber": 39, "content": "    final ordersAsync = ref.watch(getOrdersFutureProvider);", "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 98, "content": "              color: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "            ),"}, {"type": "MODIFY", "lineNumber": 102, "content": "            labelColor: Colors.white,", "oldContent": ""}, {"type": "DELETE", "lineNumber": 113, "oldContent": "              Tab(text: context.tr.myReceivedShipments),"}, {"type": "MODIFY", "lineNumber": 113, "content": "              _buildTab(context.tr.myReceivedShipments),", "oldContent": "              Tab(text: context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 114, "content": "              _buildTab(context.tr.mySentShipments),"}, {"type": "MODIFY", "lineNumber": 116, "content": "          )", "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 144, "content": "    );"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 179, "content": "    );"}, {"type": "INSERT", "lineNumber": 181, "content": "}"}]}, {"timestamp": 1757279331375, "changes": [{"type": "MODIFY", "lineNumber": 40, "content": "    Widget _buildTab(String text) {", "oldContent": ""}, {"type": "INSERT", "lineNumber": 41, "content": "      return Tab("}, {"type": "INSERT", "lineNumber": 42, "content": "        child: Container("}, {"type": "INSERT", "lineNumber": 43, "content": "          decoration: BoxDecoration("}, {"type": "INSERT", "lineNumber": 44, "content": "            border: Border.all(color: ColorManager.primaryColor, width: 1.5),"}, {"type": "INSERT", "lineNumber": 45, "content": "            borderRadius: BorderRadius.circular(7.r),"}, {"type": "INSERT", "lineNumber": 46, "content": "          ),"}, {"type": "INSERT", "lineNumber": 47, "content": "          alignment: Alignment.center,"}, {"type": "INSERT", "lineNumber": 48, "content": "          child: Text(text),"}, {"type": "INSERT", "lineNumber": 49, "content": "        ),"}, {"type": "INSERT", "lineNumber": 50, "content": "      );"}, {"type": "INSERT", "lineNumber": 51, "content": "    }"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "              Tab(text: context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 125, "content": "              _buildTab(context.tr.mySentShipments),"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 190, "content": "    );"}, {"type": "INSERT", "lineNumber": 192, "content": "}"}]}, {"timestamp": 1757279335987, "changes": [{"type": "DELETE", "lineNumber": 42, "oldContent": "    return Scaffold("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "      appBar: AppBar("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "        backgroundColor: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "        surfaceTintColor: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "        centerTitle: false,"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "        title: Text("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          context.tr.<PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "          style: AppTextStyles.title,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        leading: Pa<PERSON>("}, {"type": "INSERT", "lineNumber": 52, "content": ""}, {"type": "INSERT", "lineNumber": 53, "content": "    return Scaffold("}, {"type": "INSERT", "lineNumber": 54, "content": "      appBar: AppBar("}, {"type": "INSERT", "lineNumber": 55, "content": "        backgroundColor: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 56, "content": "        surfaceTintColor: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 57, "content": "        centerTitle: false,"}, {"type": "INSERT", "lineNumber": 58, "content": "        title: Text("}, {"type": "INSERT", "lineNumber": 59, "content": "          context.tr.<PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 60, "content": "          style: AppTextStyles.title,"}, {"type": "INSERT", "lineNumber": 61, "content": "        ),"}, {"type": "INSERT", "lineNumber": 62, "content": "        leading: Pa<PERSON>("}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            ),"}, {"type": "MODIFY", "lineNumber": 108, "content": "            indicator: BoxDecoration(", "oldContent": "            ),"}, {"type": "INSERT", "lineNumber": 109, "content": "              borderRadius: BorderRadius.circular(7.r),"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "              horizontal: AppSpaces.screenPadding,"}, {"type": "INSERT", "lineNumber": 111, "content": "            ),"}, {"type": "INSERT", "lineNumber": 126, "content": "              _buildTab(context.tr.mySentShipments),"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "          )"}, {"type": "INSERT", "lineNumber": 128, "content": "          ),"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "              _buildTab(context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 191, "content": "    );"}, {"type": "DELETE", "lineNumber": 191, "oldContent": ""}, {"type": "DELETE", "lineNumber": 193, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 194, "content": ""}]}, {"timestamp": 1757279347288, "changes": [{"type": "MODIFY", "lineNumber": 40, "content": "    Widget buildTab(String text) {", "oldContent": "    Widget _buildTab(String text) {"}, {"type": "INSERT", "lineNumber": 47, "content": "          alignment: Alignment.center,"}, {"type": "INSERT", "lineNumber": 48, "content": "          child: Text(text),"}, {"type": "INSERT", "lineNumber": 49, "content": "        ),"}, {"type": "INSERT", "lineNumber": 50, "content": "      );"}, {"type": "INSERT", "lineNumber": 51, "content": "    }"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "          alignment: Alignment.center,"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          child: Text(text),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "      );"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "    }"}, {"type": "MODIFY", "lineNumber": 125, "content": "              buildTab(context.tr.myReceivedShipments),", "oldContent": "              _buildTab(context.tr.myReceivedShipments),"}, {"type": "INSERT", "lineNumber": 126, "content": "              buildTab(context.tr.mySentShipments),"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "              _buildTab(context.tr.mySentShipments),"}, {"type": "MODIFY", "lineNumber": 129, "content": "", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 192, "content": "  }", "oldContent": "  }"}]}, {"timestamp": 1757279366122, "changes": [{"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "DELETE", "lineNumber": 48, "oldContent": ""}, {"type": "DELETE", "lineNumber": 50, "oldContent": "    return Scaffold("}, {"type": "MODIFY", "lineNumber": 53, "content": "", "oldContent": "      appBar: AppBar("}, {"type": "INSERT", "lineNumber": 54, "content": "    return Scaffold("}, {"type": "INSERT", "lineNumber": 55, "content": "      appBar: AppBar("}, {"type": "DELETE", "lineNumber": 106, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 107, "content": "          TabBar(", "oldContent": "}"}, {"type": "INSERT", "lineNumber": 108, "content": "            controller: _tabController,"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "MODIFY", "lineNumber": 113, "content": "            indicatorPadding: EdgeInsets.symmetric(horizontal: 16.w),", "oldContent": "        onTap: () {"}, {"type": "INSERT", "lineNumber": 114, "content": "            indicatorSize: TabBarIndicatorSize.tab,"}, {"type": "INSERT", "lineNumber": 129, "content": "          ),"}, {"type": "DELETE", "lineNumber": 129, "oldContent": ""}, {"type": "DELETE", "lineNumber": 152, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 153, "content": "            ),", "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 154, "content": "          ),"}, {"type": "INSERT", "lineNumber": 155, "content": "        ],"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 187, "oldContent": ""}, {"type": "INSERT", "lineNumber": 188, "content": "        onTap: () {"}, {"type": "DELETE", "lineNumber": 189, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 190, "content": "        },", "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 191, "content": "      ),"}, {"type": "INSERT", "lineNumber": 194, "content": "}"}, {"type": "INSERT", "lineNumber": 195, "content": ""}]}, {"timestamp": 1757279385915, "changes": [{"type": "MODIFY", "lineNumber": 41, "content": "    Widget buildTab(String text, bool isSelected) {", "oldContent": "    Widget buildTab(String text) {"}, {"type": "INSERT", "lineNumber": 52, "content": "    }"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "    }"}, {"type": "MODIFY", "lineNumber": 66, "content": "            bottom: AppSpaces.padding8,", "oldContent": "          AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": ""}, {"type": "DELETE", "lineNumber": 73, "oldContent": "          AnimalsListWidget(),"}, {"type": "INSERT", "lineNumber": 72, "content": "            child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 73, "content": "              child: BaseCached<PERSON><PERSON>("}, {"type": "MODIFY", "lineNumber": 130, "content": "", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 156, "content": "      ),", "oldContent": "        },"}, {"type": "MODIFY", "lineNumber": 195, "content": "", "oldContent": ""}]}, {"timestamp": 1757279397580, "changes": [{"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "DELETE", "lineNumber": 41, "oldContent": "    Widget buildTab(String text, bool isSelected) {"}, {"type": "INSERT", "lineNumber": 40, "content": "    "}, {"type": "INSERT", "lineNumber": 41, "content": "    Widget buildTab(String text, {bool isSelected = false}) {"}, {"type": "MODIFY", "lineNumber": 71, "content": "            radius: 40.r,", "oldContent": "          AppGaps.gap16,"}, {"type": "MODIFY", "lineNumber": 75, "content": "                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',", "oldContent": "          AnimalsListWidget(),"}, {"type": "MODIFY", "lineNumber": 78, "content": "            ),", "oldContent": "          // * Products"}, {"type": "INSERT", "lineNumber": 129, "content": "          ),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 155, "content": "        ],", "oldContent": "        },"}, {"type": "INSERT", "lineNumber": 194, "content": "}"}, {"type": "DELETE", "lineNumber": 195, "oldContent": ""}]}, {"timestamp": 1757279405652, "changes": [{"type": "DELETE", "lineNumber": 68, "oldContent": "          // * Home Slider"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "          HomeSliderWidget(),"}, {"type": "INSERT", "lineNumber": 68, "content": "          ),"}, {"type": "INSERT", "lineNumber": 69, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 126, "oldContent": "              buildTab(context.tr.myReceivedShipments),"}, {"type": "MODIFY", "lineNumber": 126, "content": "              buildTab(context.tr.myReceivedShipments, isSelected: true),", "oldContent": "              buildTab(context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 127, "content": "              buildTab(context.tr.mySentShipments, isSelected: false),"}, {"type": "MODIFY", "lineNumber": 152, "content": "              ],", "oldContent": "  }"}, {"type": "MODIFY", "lineNumber": 169, "content": "              color: ColorManager.greyIcon,", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 181, "content": "    }", "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 187, "content": "        order: order,", "oldContent": ""}]}, {"timestamp": 1757279416869, "changes": [{"type": "MODIFY", "lineNumber": 40, "content": "", "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 74, "content": "                height: 80.h,", "oldContent": "          AnimalsListWidget(),"}, {"type": "MODIFY", "lineNumber": 79, "content": "          ),", "oldContent": "          ProductsListWidget(),"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "              buildTab(context.tr.myReceivedShipments, isSelected: true),"}, {"type": "MODIFY", "lineNumber": 126, "content": "              buildTab(context.tr.myReceivedShipments,", "oldContent": "              buildTab(context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 127, "content": "                  isSelected: _tabController.index == 0),"}, {"type": "INSERT", "lineNumber": 128, "content": "              buildTab(context.tr.mySentShipments,"}, {"type": "INSERT", "lineNumber": 129, "content": "                  isSelected: _tabController.index == 1),"}, {"type": "DELETE", "lineNumber": 180, "oldContent": ""}, {"type": "INSERT", "lineNumber": 182, "content": "      );"}, {"type": "DELETE", "lineNumber": 186, "oldContent": ""}, {"type": "INSERT", "lineNumber": 188, "content": "      itemBuilder: (order, index) => ShipmentCardWidget("}]}, {"timestamp": 1757279428699, "changes": [{"type": "DELETE", "lineNumber": 44, "oldContent": "          decoration: BoxDecoration("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "            border: Border.all(color: ColorManager.primaryColor, width: 1.5),"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "            borderRadius: BorderRadius.circular(7.r),"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 44, "content": "          decoration: isSelected"}, {"type": "INSERT", "lineNumber": 45, "content": "              ? null"}, {"type": "INSERT", "lineNumber": 46, "content": "              : BoxDecoration("}, {"type": "INSERT", "lineNumber": 47, "content": "                  border:"}, {"type": "INSERT", "lineNumber": 48, "content": "                      Border.all(color: ColorManager.primaryColor, width: 1.5),"}, {"type": "INSERT", "lineNumber": 49, "content": "                  borderRadius: BorderRadius.circular(7.r),"}, {"type": "INSERT", "lineNumber": 50, "content": "                ),"}, {"type": "DELETE", "lineNumber": 67, "oldContent": ""}, {"type": "INSERT", "lineNumber": 70, "content": "            top: AppSpaces.padding8,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": ""}, {"type": "INSERT", "lineNumber": 73, "content": "            backgroundColor: ColorManager.lightPrimaryColor,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": ""}, {"type": "INSERT", "lineNumber": 80, "content": "              ),"}, {"type": "DELETE", "lineNumber": 80, "oldContent": ""}, {"type": "INSERT", "lineNumber": 83, "content": "        ),"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "              buildTab(context.tr.mySentShipments),"}, {"type": "INSERT", "lineNumber": 130, "content": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "            ],"}, {"type": "INSERT", "lineNumber": 133, "content": "            ],"}, {"type": "DELETE", "lineNumber": 170, "oldContent": ""}, {"type": "INSERT", "lineNumber": 173, "content": "              size: 80.w,"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "    }"}, {"type": "INSERT", "lineNumber": 186, "content": "    }"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "        order: order,"}, {"type": "INSERT", "lineNumber": 192, "content": "        order: order,"}]}, {"timestamp": 1757279434858, "changes": [{"type": "DELETE", "lineNumber": 49, "oldContent": "          alignment: Alignment.center,"}, {"type": "MODIFY", "lineNumber": 51, "content": "          alignment: Alignment.center,", "oldContent": "          child: Text(text),"}, {"type": "INSERT", "lineNumber": 52, "content": "          child: Text(text),"}, {"type": "INSERT", "lineNumber": 70, "content": "            top: AppSpaces.padding8,"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "            top: AppSpaces.padding8,"}, {"type": "INSERT", "lineNumber": 73, "content": "            backgroundColor: ColorManager.lightPrimaryColor,"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "            backgroundColor: ColorManager.lightPrimaryColor,"}, {"type": "INSERT", "lineNumber": 80, "content": "              ),"}, {"type": "MODIFY", "lineNumber": 83, "content": "        ),", "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "        ),"}, {"type": "MODIFY", "lineNumber": 131, "content": "              buildTab(context.tr.mySentShipments,", "oldContent": "              buildTab(context.tr.mySentShipments,"}, {"type": "INSERT", "lineNumber": 133, "content": "            ],"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "            ],"}, {"type": "INSERT", "lineNumber": 173, "content": "              size: 80.w,"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "              size: 80.w,"}, {"type": "INSERT", "lineNumber": 186, "content": "    }"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "    }"}, {"type": "INSERT", "lineNumber": 192, "content": "        order: order,"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "        order: order,"}]}, {"timestamp": 1757279735170, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';"}, {"type": "MODIFY", "lineNumber": 1, "content": "import 'package:flutter_hooks/flutter_hooks.dart';", "oldContent": "import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 2, "content": "import 'package:hooks_riverpod/hooks_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "import 'package:dropx/src/screens/orders/providers/order.providers.dart';"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "import 'package:dropx/src/screens/orders/models/order.model.dart';"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "import 'package:google_fonts/google_fonts.dart';"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "import '../widgets/shipment_card.widget.dart';"}, {"type": "INSERT", "lineNumber": 8, "content": "import '../widgets/shipments_tab_bar.widget.dart';"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "class HomeScreen extends ConsumerStatefulWidget {"}, {"type": "INSERT", "lineNumber": 10, "content": "class HomeScreen extends HookConsumerWidget {"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  ConsumerState<HomeScreen> createState() => _HomeScreenState();"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 20, "oldContent": ""}, {"type": "INSERT", "lineNumber": 42, "content": "                ),"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "              ),"}, {"type": "INSERT", "lineNumber": 76, "content": "        actions: ["}, {"type": "INSERT", "lineNumber": 122, "content": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "              buildTab(context.tr.mySentShipments,"}]}, {"timestamp": 1757279744966, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import '../widgets/shipments_tab_bar.widget.dart';"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "class HomeScreen extends HookConsumerWidget {"}, {"type": "INSERT", "lineNumber": 8, "content": "import '../widgets/shipments_tab_bar.widget.dart';"}, {"type": "INSERT", "lineNumber": 10, "content": "class HomeScreen extends HookConsumerWidget {"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "class _HomeScreenState extends ConsumerState<HomeScreen>"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "    with SingleTickerProviderStateMixin {"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  late TabController _tabController;"}, {"type": "DELETE", "lineNumber": 16, "oldContent": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  void initState() {"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "    _tabController = TabController(length: 2, vsync: this);"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 22, "oldContent": ""}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  void dispose() {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    _tabController.dispose();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    super.dispose();"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "    final ordersAsync = ref.watch(getOrdersFutureProvider);"}, {"type": "DELETE", "lineNumber": 32, "oldContent": ""}, {"type": "INSERT", "lineNumber": 14, "content": "  Widget build(BuildContext context, WidgetRef ref) {"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 24, "content": "                ),"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        actions: ["}, {"type": "INSERT", "lineNumber": 58, "content": "        actions: ["}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                  isSelected: _tabController.index == 0),"}, {"type": "INSERT", "lineNumber": 104, "content": "                  isSelected: _tabController.index == 0),"}]}, {"timestamp": 1757279760857, "changes": [{"type": "INSERT", "lineNumber": 4, "content": "import 'package:dropx/src/core/shared/extensions/context_extensions.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/src/core/theme/color_manager.dart';"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import '../widgets/shipments_tab_bar.widget.dart';"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "class HomeScreen extends HookConsumerWidget {"}, {"type": "INSERT", "lineNumber": 8, "content": "import '../widgets/shipments_tab_bar.widget.dart';"}, {"type": "INSERT", "lineNumber": 10, "content": "class HomeScreen extends HookConsumerWidget {"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  Widget build(BuildContext context, WidgetRef ref) {"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    Widget buildTab(String text, {bool isSelected = false}) {"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "      return Tab("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "        child: Container("}, {"type": "DELETE", "lineNumber": 19, "oldContent": "          decoration: isSelected"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "              ? null"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "              : BoxDecoration("}, {"type": "DELETE", "lineNumber": 22, "oldContent": "                  border:"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "                      Border.all(color: ColorManager.primaryColor, width: 1.5),"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "                  borderRadius: BorderRadius.circular(7.r),"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "          alignment: Alignment.center,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "          child: Text(text),"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "      );"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 30, "oldContent": ""}, {"type": "INSERT", "lineNumber": 14, "content": "  Widget build(BuildContext context, WidgetRef ref) {"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "        actions: ["}, {"type": "DELETE", "lineNumber": 59, "oldContent": ""}, {"type": "DELETE", "lineNumber": 60, "oldContent": "          // * Stores"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "          HomeStoresListWidget(),"}, {"type": "INSERT", "lineNumber": 42, "content": "        actions: ["}, {"type": "INSERT", "lineNumber": 43, "content": "          IconButton("}, {"type": "INSERT", "lineNumber": 44, "content": "            onPressed: () {},"}, {"type": "INSERT", "lineNumber": 45, "content": "            icon: const I<PERSON>(Icons.notifications),"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "          // * Doctors"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "          DoctorsListWidget(),"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "          AppGaps.gap24,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 49, "content": "      body: Column("}, {"type": "INSERT", "lineNumber": 50, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 51, "content": "          // Active Shipments Header"}, {"type": "INSERT", "lineNumber": 52, "content": "          Container("}, {"type": "INSERT", "lineNumber": 53, "content": "            width: double.infinity,"}, {"type": "INSERT", "lineNumber": 54, "content": "            padding: EdgeInsets.symmetric("}, {"type": "INSERT", "lineNumber": 55, "content": "              horizontal: AppSpaces.screenPadding,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": ""}, {"type": "INSERT", "lineNumber": 58, "content": "            child: Text("}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                  isSelected: _tabController.index == 0),"}, {"type": "INSERT", "lineNumber": 88, "content": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": ""}, {"type": "DELETE", "lineNumber": 124, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 107, "content": "                ),"}, {"type": "INSERT", "lineNumber": 108, "content": "                // Sent Shipments Tab"}, {"type": "INSERT", "lineNumber": 109, "content": "                ordersAsync.get("}, {"type": "DELETE", "lineNumber": 127, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "      );"}, {"type": "INSERT", "lineNumber": 111, "content": "                    ordersResponse.data.received,"}, {"type": "INSERT", "lineNumber": 112, "content": "                    context.tr.noSentShipments,"}, {"type": "INSERT", "lineNumber": 113, "content": "                  ),"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "        order: order,"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "      itemBuilder: (order, index) => ShipmentCardWidget("}, {"type": "DELETE", "lineNumber": 144, "oldContent": "      padding: EdgeInsets.only(bottom: 100.h),"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "              emptyMessage,"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "            Text("}, {"type": "INSERT", "lineNumber": 126, "content": "        child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 127, "content": "          mainAxisAlignment: MainAxisAlignment.center,"}, {"type": "INSERT", "lineNumber": 128, "content": "          children: ["}, {"type": "INSERT", "lineNumber": 129, "content": "            Icon("}, {"type": "INSERT", "lineNumber": 130, "content": "              Icons.inventory_2_outlined,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": ""}, {"type": "DELETE", "lineNumber": 150, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 151, "oldContent": "                color: ColorManager.greyText,"}, {"type": "DELETE", "lineNumber": 152, "oldContent": ""}, {"type": "INSERT", "lineNumber": 133, "content": "            ),"}, {"type": "INSERT", "lineNumber": 134, "content": "            AppGaps.gap16,"}, {"type": "INSERT", "lineNumber": 135, "content": "            Text("}, {"type": "INSERT", "lineNumber": 136, "content": "              emptyMessage,"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 156, "oldContent": ""}, {"type": "DELETE", "lineNumber": 157, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 138, "content": "                color: ColorManager.greyText,"}, {"type": "INSERT", "lineNumber": 139, "content": "              ),"}, {"type": "INSERT", "lineNumber": 140, "content": "            ),"}, {"type": "INSERT", "lineNumber": 141, "content": "          ],"}, {"type": "INSERT", "lineNumber": 142, "content": "        ),"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 146, "content": "    return BaseList<OrderModel>("}, {"type": "INSERT", "lineNumber": 147, "content": "      data: orders,"}, {"type": "INSERT", "lineNumber": 148, "content": "      padding: EdgeInsets.only(bottom: 100.h),"}, {"type": "INSERT", "lineNumber": 152, "content": "          // TODO: Navigate to shipment details"}, {"type": "INSERT", "lineNumber": 153, "content": "        },"}, {"type": "INSERT", "lineNumber": 154, "content": "      ),"}, {"type": "INSERT", "lineNumber": 155, "content": "    );"}, {"type": "INSERT", "lineNumber": 156, "content": "  }"}, {"type": "INSERT", "lineNumber": 158, "content": ""}]}, {"timestamp": 1757279779604, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import 'package:xr_helper/xr_helper.dart';", "oldContent": "import 'widgets/products_list.widget.dart';"}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        actions: ["}, {"type": "DELETE", "lineNumber": 27, "oldContent": "          IconButton("}, {"type": "DELETE", "lineNumber": 29, "oldContent": "            onPressed: () {},"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "            icon: const I<PERSON>(Icons.notifications),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "      body: Column("}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        children: ["}, {"type": "DELETE", "lineNumber": 40, "oldContent": "          // Active Shipments Header"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "          Container("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "            width: double.infinity,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "            padding: EdgeInsets.symmetric("}, {"type": "DELETE", "lineNumber": 48, "oldContent": "              horizontal: AppSpaces.screenPadding,"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "            child: Text("}, {"type": "INSERT", "lineNumber": 42, "content": "        actions: ["}, {"type": "INSERT", "lineNumber": 43, "content": "          IconButton("}, {"type": "INSERT", "lineNumber": 44, "content": "            onPressed: () {},"}, {"type": "INSERT", "lineNumber": 45, "content": "            icon: const I<PERSON>(Icons.notifications),"}, {"type": "INSERT", "lineNumber": 49, "content": "      body: Column("}, {"type": "INSERT", "lineNumber": 50, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 51, "content": "          // Active Shipments Header"}, {"type": "INSERT", "lineNumber": 52, "content": "          Container("}, {"type": "INSERT", "lineNumber": 53, "content": "            width: double.infinity,"}, {"type": "INSERT", "lineNumber": 54, "content": "            padding: EdgeInsets.symmetric("}, {"type": "INSERT", "lineNumber": 55, "content": "              horizontal: AppSpaces.screenPadding,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 57, "content": "            ),"}, {"type": "INSERT", "lineNumber": 58, "content": "            child: Text("}, {"type": "INSERT", "lineNumber": 67, "content": "          // Shipments TabBar Widget"}, {"type": "INSERT", "lineNumber": 68, "content": "          const Expanded("}, {"type": "INSERT", "lineNumber": 69, "content": "            child: ShipmentsTabBarWidget(),"}, {"type": "INSERT", "lineNumber": 70, "content": "          ),"}, {"type": "INSERT", "lineNumber": 71, "content": "        ],"}, {"type": "INSERT", "lineNumber": 72, "content": "      ),"}, {"type": "INSERT", "lineNumber": 73, "content": "    );"}, {"type": "INSERT", "lineNumber": 74, "content": "  }"}, {"type": "INSERT", "lineNumber": 75, "content": "}"}, {"type": "INSERT", "lineNumber": 76, "content": ""}, {"type": "DELETE", "lineNumber": 71, "oldContent": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                  data: (ordersResponse) => _buildShipmentsList("}, {"type": "INSERT", "lineNumber": 87, "content": "            unselectedLabelColor: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 92, "content": "            unselectedLabelStyle: AppTextStyles.bodyMedium.copyWith("}, {"type": "DELETE", "lineNumber": 85, "oldContent": "        },"}, {"type": "INSERT", "lineNumber": 94, "content": "            ),"}, {"type": "INSERT", "lineNumber": 98, "content": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                // Sent Shipments Tab"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                ordersAsync.get("}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                    ordersResponse.data.received,"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "                    context.tr.noSentShipments,"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 107, "oldContent": ""}, {"type": "DELETE", "lineNumber": 108, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 111, "content": "                // Received Shipments Tab"}, {"type": "INSERT", "lineNumber": 112, "content": "                ordersAsync.get("}, {"type": "INSERT", "lineNumber": 113, "content": "                  data: (ordersResponse) => _buildShipmentsList("}, {"type": "INSERT", "lineNumber": 114, "content": "                    ordersResponse.data.created,"}, {"type": "INSERT", "lineNumber": 115, "content": "                    context.tr.noReceivedShipments,"}, {"type": "INSERT", "lineNumber": 116, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 117, "content": "                ),"}, {"type": "INSERT", "lineNumber": 118, "content": "                // Sent Shipments Tab"}, {"type": "INSERT", "lineNumber": 119, "content": "                ordersAsync.get("}, {"type": "INSERT", "lineNumber": 120, "content": "                  data: (ordersResponse) => _buildShipmentsList("}, {"type": "INSERT", "lineNumber": 121, "content": "                    ordersResponse.data.received,"}, {"type": "INSERT", "lineNumber": 122, "content": "                    context.tr.noSentShipments,"}, {"type": "INSERT", "lineNumber": 123, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 124, "content": "                ),"}, {"type": "INSERT", "lineNumber": 125, "content": "              ],"}, {"type": "INSERT", "lineNumber": 126, "content": "            ),"}, {"type": "INSERT", "lineNumber": 127, "content": "          ),"}, {"type": "INSERT", "lineNumber": 128, "content": "        ],"}, {"type": "INSERT", "lineNumber": 129, "content": "      ),"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "        onTap: () {"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "        order: order,"}, {"type": "INSERT", "lineNumber": 131, "content": "  }"}, {"type": "INSERT", "lineNumber": 132, "content": ""}, {"type": "INSERT", "lineNumber": 133, "content": "  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {"}, {"type": "INSERT", "lineNumber": 134, "content": "    if (orders.isEmpty) {"}, {"type": "INSERT", "lineNumber": 135, "content": "      return Center("}, {"type": "DELETE", "lineNumber": 114, "oldContent": "            Text("}, {"type": "DELETE", "lineNumber": 119, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 141, "content": "              size: 80.w,"}, {"type": "INSERT", "lineNumber": 142, "content": "              color: ColorManager.greyIcon,"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 147, "content": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "DELETE", "lineNumber": 132, "oldContent": ""}, {"type": "DELETE", "lineNumber": 134, "oldContent": "  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "DELETE", "lineNumber": 138, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 153, "content": "      );"}, {"type": "INSERT", "lineNumber": 154, "content": "    }"}, {"type": "INSERT", "lineNumber": 155, "content": ""}, {"type": "DELETE", "lineNumber": 142, "oldContent": "              size: 80.w,"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "              color: ColorManager.greyIcon,"}, {"type": "INSERT", "lineNumber": 159, "content": "      itemBuilder: (order, index) => ShipmentCardWidget("}, {"type": "INSERT", "lineNumber": 160, "content": "        order: order,"}, {"type": "INSERT", "lineNumber": 161, "content": "        onTap: () {"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 167, "content": "}"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "    }"}]}, {"timestamp": 1757279800823, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "", "oldContent": "class HomeScreen extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "        children: const ["}, {"type": "INSERT", "lineNumber": 25, "content": "          padding: const EdgeInsets.only("}, {"type": "INSERT", "lineNumber": 26, "content": "            right: AppSpaces.padding8,"}, {"type": "INSERT", "lineNumber": 35, "content": "                height: 80.h,"}, {"type": "INSERT", "lineNumber": 36, "content": "                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',"}, {"type": "INSERT", "lineNumber": 37, "content": "                fit: BoxFit.cover,"}, {"type": "INSERT", "lineNumber": 38, "content": "              ),"}, {"type": "INSERT", "lineNumber": 39, "content": "            ),"}, {"type": "INSERT", "lineNumber": 40, "content": "          ),"}, {"type": "INSERT", "lineNumber": 41, "content": "        ),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                height: 80.h,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "          AppGaps.gap24,"}, {"type": "INSERT", "lineNumber": 46, "content": "          ),"}, {"type": "INSERT", "lineNumber": 47, "content": "        ],"}, {"type": "INSERT", "lineNumber": 48, "content": "      ),"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "      ),"}, {"type": "MODIFY", "lineNumber": 57, "content": "            ),", "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "      );"}, {"type": "INSERT", "lineNumber": 60, "content": "              style: AppTextStyles.headlineSmall.copyWith("}, {"type": "INSERT", "lineNumber": 61, "content": "                fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 62, "content": "              ),"}, {"type": "INSERT", "lineNumber": 63, "content": "              textAlign: TextAlign.center,"}, {"type": "INSERT", "lineNumber": 64, "content": "            ),"}, {"type": "INSERT", "lineNumber": 65, "content": "          ),"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "          TabBar("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "            controller: _tabController,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "            indicator: BoxDecoration("}, {"type": "DELETE", "lineNumber": 77, "oldContent": "              borderRadius: BorderRadius.circular(7.r),"}, {"type": "DELETE", "lineNumber": 79, "oldContent": "              color: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "            indicatorPadding: EdgeInsets.symmetric(horizontal: 16.w),"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "            indicatorSize: TabBarIndicatorSize.tab,"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "            labelColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "            labelStyle: AppTextStyles.bodyMedium.copyWith("}, {"type": "DELETE", "lineNumber": 88, "oldContent": "              fontWeight: FontWeight.w600,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "              fontFamily: GoogleFonts.cairo().fontFamily,"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "              fontFamily: GoogleFonts.cairo().fontFamily,"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "            unselectedLabelColor: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            tabs: ["}, {"type": "DELETE", "lineNumber": 95, "oldContent": "              buildTab(context.tr.myReceivedShipments,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "              buildTab(context.tr.mySentShipments,"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "                  isSelected: _tabController.index == 1),"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "            unselectedLabelStyle: AppTextStyles.bodyMedium.copyWith("}, {"type": "DELETE", "lineNumber": 99, "oldContent": "            ],"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 102, "oldContent": ""}, {"type": "DELETE", "lineNumber": 103, "oldContent": "            Text("}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                  isSelected: _tabController.index == 0),"}, {"type": "DELETE", "lineNumber": 105, "oldContent": ""}, {"type": "DELETE", "lineNumber": 106, "oldContent": "          // Tab Bar View"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "          Expanded("}, {"type": "DELETE", "lineNumber": 108, "oldContent": ""}, {"type": "DELETE", "lineNumber": 109, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "      return Center("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "                // Received Shipments Tab"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "                ordersAsync.get("}, {"type": "DELETE", "lineNumber": 114, "oldContent": "                  data: (ordersResponse) => _buildShipmentsList("}, {"type": "DELETE", "lineNumber": 115, "oldContent": "        child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                    ordersResponse.data.created,"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "                    context.tr.noReceivedShipments,"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "          mainAxisAlignment: MainAxisAlignment.center,"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "          children: ["}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "            Icon("}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                // Sent Shipments Tab"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "              Icons.inventory_2_outlined,"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "                ordersAsync.get("}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                  data: (ordersResponse) => _buildShipmentsList("}, {"type": "DELETE", "lineNumber": 127, "oldContent": "                    ordersResponse.data.received,"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "                    context.tr.noSentShipments,"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "            AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "            Text("}, {"type": "DELETE", "lineNumber": 136, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "              emptyMessage,"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "                color: ColorManager.greyText,"}, {"type": "DELETE", "lineNumber": 142, "oldContent": ""}, {"type": "DELETE", "lineNumber": 143, "oldContent": "  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "    if (orders.isEmpty) {"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "      return Center("}, {"type": "DELETE", "lineNumber": 147, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "          ],"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "    return BaseList<OrderModel>("}, {"type": "DELETE", "lineNumber": 151, "oldContent": "              size: 80.w,"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "      data: orders,"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "              color: ColorManager.greyIcon,"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "      padding: EdgeInsets.only(bottom: 100.h),"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "          // TODO: Navigate to shipment details"}, {"type": "DELETE", "lineNumber": 156, "oldContent": "        },"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "              style: AppTextStyles.bodyLarge.copyWith("}, {"type": "DELETE", "lineNumber": 158, "oldContent": "        onTap: () {"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "        order: order,"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "      itemBuilder: (order, index) => ShipmentCardWidget("}, {"type": "DELETE", "lineNumber": 161, "oldContent": ""}, {"type": "DELETE", "lineNumber": 162, "oldContent": "      );"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';\nimport 'package:dropx/src/screens/home/<USER>/home.screen.dart';\n\nimport '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';\n\nclass MainScreen extends ConsumerWidget {\n  const MainScreen({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final currentIndex = ref.watch(bottomNavigationControllerProvider);\n    return Scaffold(\n      body: _SelectedScreen(currentIndex: currentIndex),\n      bottomNavigationBar: const BottomNavBarWidget(),\n    );\n  }\n}\n\nString selectedTitle(int currentIndex, BuildContext context) {\n  switch (currentIndex) {\n    case 0:\n      return context.tr.home;\n    case 1:\n      return context.tr.reels;\n    case 2:\n      return context.tr.doctors;\n    case 3:\n      return context.tr.shops;\n  }\n\n  return context.tr.home;\n}\n\nclass _SelectedScreen extends StatelessWidget {\n  final int currentIndex;\n\n  const _SelectedScreen({required this.currentIndex});\n\n  @override\n  Widget build(BuildContext context) {\n    switch (currentIndex) {\n      case 0:\n        return const HomeScreen();\n      case 1:\n        return const ReelsScreen();\n    }\n    return const SizedBox.shrink();\n  }\n}\n", "baseTimestamp": 1756930341284, "deltas": [{"timestamp": 1756930344323, "changes": [{"type": "DELETE", "lineNumber": 46, "oldContent": "      case 1:"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        return const <PERSON><PERSON>S<PERSON><PERSON>();"}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756930639830, "changes": [{"type": "MODIFY", "lineNumber": 39, "content": "  const _SelectedScreen({", "oldContent": "  const _SelectedScreen({required this.currentIndex});"}, {"type": "INSERT", "lineNumber": 40, "content": "    required this.currentIndex,"}, {"type": "INSERT", "lineNumber": 41, "content": "  });"}, {"type": "DELETE", "lineNumber": 46, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass ProductsListWidget extends StatelessWidget {\n  const ProductsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    final width = 140.w;\n\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.products, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const ProductsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Stack(\n                    children: [\n                      BaseCachedImage(\n                        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyXeKDN29AmZgZPLS7n0Bepe8QmVappBwZCeA3XWEbWNdiDFB',\n                        width: width,\n                        height: 110.h,\n                        radius: AppRadius.radius20,\n                      ),\n                      Container(\n                        width: width,\n                        padding: const EdgeInsets.symmetric(\n                          horizontal: AppSpaces.padding12,\n                          vertical: AppSpaces.padding8,\n                        ),\n                        alignment: Alignment.bottomLeft,\n                        decoration: BoxDecoration(\n                          color: Colors.black.withOpacity(0.2),\n                          borderRadius:\n                              BorderRadius.circular(AppSpaces.padding20),\n                        ),\n                        child: Row(\n                          mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                          crossAxisAlignment: CrossAxisAlignment.end,\n                          children: [\n                            Expanded(\n                              child: Column(\n                                crossAxisAlignment: CrossAxisAlignment.start,\n                                mainAxisSize: MainAxisSize.min,\n                                children: [\n                                  Expanded(\n                                    child: Align(\n                                      alignment: Alignment.bottomLeft,\n                                      child: Text(\n                                        'Dogs Food',\n                                        style: AppTextStyles.title\n                                            .copyWith(color: Colors.white),\n                                      ),\n                                    ),\n                                  ),\n                                  Text(\n                                    '\\$20',\n                                    style: AppTextStyles.title.copyWith(\n                                        color: ColorManager.primaryColor),\n                                  ),\n                                ],\n                              ),\n                            ),\n                            CircleAvatar(\n                              backgroundColor: ColorManager.primaryColor,\n                              radius: 14.r,\n                              child: const Icon(\n                                Icons.add_shopping_cart_outlined,\n                                color: Colors.white,\n                                size: 18,\n                              ),\n                            ),\n                          ],\n                        ),\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930371310, "deltas": [{"timestamp": 1756930376171, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/products.screen.dart';"}]}, {"timestamp": 1756930389063, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/stores.screen.dart';", "oldContent": "import '../../../stores/view/products_screen/products.screen.dart';"}]}]}, "/a.dummy": {"filePath": "/a.dummy", "baseContent": "stores.screen.dart", "baseTimestamp": 1756930385283}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/app_bar/base_appbar.dart';\nimport 'package:dropx/src/core/shared/widgets/search_bar_widget/search_bar.widget.dart';\nimport 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsScreen extends StatelessWidget {\n  const ProductsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: BaseAppBar(\n        title: context.tr.stores,\n      ),\n      body: Padding(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        child: Column(\n          children: [\n            AppGaps.gap12,\n\n            // * Search Bar\n            SearchBarWidget(\n              label: context.tr.searchForProducts,\n            ),\n\n            AppGaps.gap12,\n\n            // * Products Grid\n            const Expanded(child: ProductsGridWidget()),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930392570, "deltas": [{"timestamp": 1756930397625, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart';"}]}, {"timestamp": 1756930435762, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart", "baseContent": "import 'package:flutter/cupertino.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoreDetailsScreen extends StatelessWidget {\n  const StoreDetailsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: Column(\n        children: [\n          // * Store Top Section (Image - Fav - Back Arrow)\n          const StoreTopSectionDetailsWidget(),\n\n          AppGaps.gap24,\n\n          // * Store Tabs\n          Expanded(\n            child: ListView(\n              padding:\n                  const EdgeInsets.symmetric(horizontal: AppSpaces.padding12),\n              children: [\n                // about the store\n                Row(\n                  mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                  children: [\n                    // * About\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            color: ColorManager.primaryColor.withOpacity(.8),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            CupertinoIcons.info,\n                            color: ColorManager.white,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.about,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Products\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.shopping_bag_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.products,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Reels\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.video_collection_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          'Reels',\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Animals\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.pets_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.animals,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                  ],\n                ),\n              ],\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930405920, "deltas": [{"timestamp": 1756930408090, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/product_details_screen/widgets/store_top_section_details.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/lists/base_list.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/stores/providers/store.providers.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsGridWidget extends ConsumerWidget {\n  const ProductsGridWidget({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final storesFuture = ref.watch(getStoresFutureProvider);\n\n    return BaseList.grid(\n      data: const [0, 1, 2, 3, 4],\n      crossAxisCount: 2,\n      mainAxisSpacing: AppSpaces.padding16,\n      itemBuilder: (context, index) => const ProductCard(),\n      separatorGap: AppGaps.gap16,\n    );\n\n    return storesFuture.get(\n      data: (stores) {\n        return BaseList(\n          data: stores,\n          isGrid: true,\n          itemBuilder: (context, index) => GestureDetector(\n            onTap: () {\n              const StoreDetailsScreen().navigate;\n            },\n            child: Stack(\n              children: [\n                BaseCachedImage(\n                  'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',\n                  height: 120.h,\n                  width: 220.w,\n                  radius: AppRadius.radius20,\n                ),\n                Container(\n                  width: 220.w,\n                  padding: const EdgeInsets.symmetric(\n                      horizontal: AppSpaces.padding12,\n                      vertical: AppSpaces.padding8),\n                  alignment: Alignment.bottomCenter,\n                  decoration: BoxDecoration(\n                      color: Colors.black.withOpacity(0.2),\n                      borderRadius: BorderRadius.circular(AppRadius.radius20)),\n                  child: Row(\n                    mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                    children: [\n                      Text(\n                        'Pet Store',\n                        style: AppTextStyles.whiteTitle.copyWith(\n                          fontSize: 20,\n                        ),\n                      ),\n                      const CircleAvatar(\n                        radius: 16,\n                        backgroundColor: ColorManager.primaryColor,\n                        child: Icon(\n                          Icons.arrow_forward_ios,\n                          color: Colors.white,\n                          size: 18,\n                        ),\n                      )\n                    ],\n                  ),\n                )\n              ],\n            ),\n          ),\n          separatorGap: AppGaps.gap16,\n        );\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756930414434, "deltas": [{"timestamp": 1756930418181, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/product_card.dart';"}]}, {"timestamp": 1756930420500, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "    return BaseList.grid("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      data: const [0, 1, 2, 3, 4],"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "      crossAxisCount: 2,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      mainAxisSpacing: AppSpaces.padding16,"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      itemBuilder: (context, index) => const ProductCard(),"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      separatorGap: AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 24, "oldContent": ""}]}, {"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 10, "content": "import '../../product_details_screen/store_details.screen.dart';"}, {"type": "INSERT", "lineNumber": 11, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"تسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"هل نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الجوال\",\n  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"ريلز\",\n  \"shops\": \"المحلات\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحبًا، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\",\n  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\",\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": ""}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 30, "content": "}"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"تسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"هل نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الجوال\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"ريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المحلات\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحبًا، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"Login\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"Register\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"Email\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"Password\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"Reset Password\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"Ski<PERSON>\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"Stores\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"Full Name\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"Home\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"Reels\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"Shops\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"Doctors\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"See All\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"Animals\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"Products\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"About\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"Next\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"Start Now\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"Description\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"Address\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"Enter\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"Save\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"Submit\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"Pick Image\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"Pick Location\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"Change Location\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"No data found\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"Change Social\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"Social Media\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"Store Logo\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"Store Background\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"Store Name\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"Search\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"Search for products\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"Search for stores\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"Search for doctors\""}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"Login\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"Register\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"Email\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"Password\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"Reset Password\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"Ski<PERSON>\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"Stores\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"Full Name\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"Home\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"Reels\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"Shops\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"Doctors\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"See All\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"Animals\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"Products\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"About\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"التسجيل\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"كلمة المرور\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"تخطي\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"الاسم الكامل\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"الرئيسية\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"الريلز\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"الأطباء\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"عرض الكل\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"الحيوانات\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"المنتجات\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"حول\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"التالي\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"الوصف\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"أدخل\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"حفظ\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"إرسال\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"اختيار صورة\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"اسم المتجر\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"البح<PERSON>\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"searchForDoctors\": \"Search for doctors\""}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"searchForStores\": \"Search for stores\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"next\": \"Next\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"searchForProducts\": \"Search for products\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"startNow\": \"Start Now\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"search\": \"Search\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"description\": \"Description\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"storeName\": \"Store Name\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"address\": \"Address\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"storeBackground\": \"Store Background\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"enter\": \"Enter\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"storeLogo\": \"Store Logo\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"save\": \"Save\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"submit\": \"Submit\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"pickImage\": \"Pick Image\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"socialMedia\": \"Social Media\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"pickLocation\": \"Pick Location\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"changeSocial\": \"Change Social\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"noDataFound\": \"No data found\","}, {"type": "DELETE", "lineNumber": 64, "oldContent": "  \"changeLocation\": \"Change Location\","}]}, {"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 19, "content": "  \"didntReceiveCode\": \"لم تتلق رمز التحقق؟\","}, {"type": "DELETE", "lineNumber": 32, "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 64, "content": "  \"searchForDoctors\": \"البحث عن الأطباء\",", "oldContent": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "INSERT", "lineNumber": 65, "content": "  \"createAccount\": \"إنشاء حساب\","}, {"type": "INSERT", "lineNumber": 66, "content": "  \"idNumber\": \"رقم الهوية\","}, {"type": "INSERT", "lineNumber": 67, "content": "  \"termsAndConditions\": \"أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق\","}, {"type": "INSERT", "lineNumber": 68, "content": "  \"invalidIdNumber\": \"رقم الهوية غير صحيح\","}, {"type": "INSERT", "lineNumber": 69, "content": "  \"invalidPhoneNumber\": \"رقم الهاتف غير صحيح\","}, {"type": "INSERT", "lineNumber": 70, "content": "  \"passwordsDoNotMatch\": \"كلمات المرور غير متطابقة\","}, {"type": "INSERT", "lineNumber": 71, "content": "  \"pleaseAcceptTerms\": \"يرجى الموافقة على الشروط والأحكام\","}, {"type": "INSERT", "lineNumber": 72, "content": "  \"error\": \"خطأ\","}, {"type": "INSERT", "lineNumber": 73, "content": "  \"termsAndConditionsText\": \"أقر بأنني قد قرأت وفهمت وأوافق على\","}, {"type": "INSERT", "lineNumber": 74, "content": "  \"termsAndConditionsLink\": \"الشروط والأحكام\","}, {"type": "INSERT", "lineNumber": 75, "content": "  \"termsAndConditionsEnd\": \"الخاصة باستخدام هذا التطبيق\","}, {"type": "INSERT", "lineNumber": 76, "content": "  \"termsDialogTitle\": \"الشروط والأحكام\","}, {"type": "INSERT", "lineNumber": 77, "content": "  \"termsDialogContent\": \"هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.\","}, {"type": "INSERT", "lineNumber": 78, "content": "  \"close\": \"إغلاق\","}, {"type": "INSERT", "lineNumber": 79, "content": "  \"phoneHint\": \"05xxxxxxxx\","}, {"type": "INSERT", "lineNumber": 80, "content": "  \"loginTitle\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 81, "content": "  \"rememberMe\": \"تذكرني\","}, {"type": "INSERT", "lineNumber": 82, "content": "  \"forgotPasswordLink\": \"نسيت كلمة المرور؟\","}, {"type": "INSERT", "lineNumber": 83, "content": "  \"dontHaveAccount\": \"لا تملك حساب؟\","}, {"type": "INSERT", "lineNumber": 84, "content": "  \"verificationTitle\": \"التحقق من الهاتف\","}, {"type": "INSERT", "lineNumber": 85, "content": "  \"verificationMessage\": \"تم إرسال رمز التحقق إلى رقم هاتفك\","}, {"type": "INSERT", "lineNumber": 86, "content": "  \"enterVerificationCode\": \"أدخل رمز التحقق\","}, {"type": "INSERT", "lineNumber": 87, "content": "  \"verify\": \"تحقق\","}, {"type": "INSERT", "lineNumber": 88, "content": "  \"resendCode\": \"إعادة إرسال الرمز\","}, {"type": "INSERT", "lineNumber": 89, "content": "  \"verificationCodeSent\": \"تم إرسال رمز التحقق بنجاح\","}, {"type": "INSERT", "lineNumber": 90, "content": "  \"invalidVerificationCode\": \"رمز التحقق غير صحيح\","}, {"type": "INSERT", "lineNumber": 91, "content": "  \"loginSuccessful\": \"تم تسجيل الدخول بنجاح\","}, {"type": "INSERT", "lineNumber": 92, "content": "  \"registrationSuccessful\": \"تم التسجيل بنجاح\""}, {"type": "INSERT", "lineNumber": 93, "content": "}"}]}, {"timestamp": 1757277829268, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "  \"registrationSuccessful\": \"تم التسجيل بنجاح\""}, {"type": "DELETE", "lineNumber": 70, "oldContent": "  \"loginSuccessful\": \"تم تسجيل الدخول بنجاح\","}, {"type": "DELETE", "lineNumber": 72, "oldContent": "  \"invalidVerificationCode\": \"رمز التحقق غير صحيح\","}, {"type": "DELETE", "lineNumber": 74, "oldContent": "  \"verificationCodeSent\": \"تم إرسال رمز التحقق بنجاح\","}, {"type": "DELETE", "lineNumber": 76, "oldContent": "  \"resendCode\": \"إعادة إرسال الرمز\","}, {"type": "DELETE", "lineNumber": 78, "oldContent": "  \"verify\": \"تحقق\","}, {"type": "DELETE", "lineNumber": 80, "oldContent": "  \"enterVerificationCode\": \"أدخل رمز التحقق\","}, {"type": "DELETE", "lineNumber": 82, "oldContent": "  \"verificationMessage\": \"تم إرسال رمز التحقق إلى رقم هاتفك\","}, {"type": "DELETE", "lineNumber": 84, "oldContent": "  \"verificationTitle\": \"التحقق من الهاتف\","}, {"type": "DELETE", "lineNumber": 86, "oldContent": "  \"dontHaveAccount\": \"لا تملك حساب؟\","}, {"type": "DELETE", "lineNumber": 88, "oldContent": "  \"forgotPasswordLink\": \"نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 90, "oldContent": "  \"rememberMe\": \"تذكرني\","}, {"type": "DELETE", "lineNumber": 92, "oldContent": "  \"loginTitle\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 80, "content": "  \"loginTitle\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 81, "content": "  \"rememberMe\": \"تذكرني\","}, {"type": "INSERT", "lineNumber": 82, "content": "  \"forgotPasswordLink\": \"نسيت كلمة المرور؟\","}, {"type": "INSERT", "lineNumber": 83, "content": "  \"dontHaveAccount\": \"لا تملك حساب؟\","}, {"type": "INSERT", "lineNumber": 84, "content": "  \"verificationTitle\": \"التحقق من الهاتف\","}, {"type": "INSERT", "lineNumber": 85, "content": "  \"verificationMessage\": \"تم إرسال رمز التحقق إلى رقم هاتفك\","}, {"type": "INSERT", "lineNumber": 86, "content": "  \"enterVerificationCode\": \"أدخل رمز التحقق\","}, {"type": "INSERT", "lineNumber": 87, "content": "  \"verify\": \"تحقق\","}, {"type": "INSERT", "lineNumber": 88, "content": "  \"resendCode\": \"إعادة إرسال الرمز\","}, {"type": "INSERT", "lineNumber": 89, "content": "  \"verificationCodeSent\": \"تم إرسال رمز التحقق بنجاح\","}, {"type": "INSERT", "lineNumber": 90, "content": "  \"invalidVerificationCode\": \"رمز التحقق غير صحيح\","}, {"type": "INSERT", "lineNumber": 91, "content": "  \"loginSuccessful\": \"تم تسجيل الدخول بنجاح\","}, {"type": "INSERT", "lineNumber": 92, "content": "  \"registrationSuccessful\": \"تم التسجيل بنجاح\","}, {"type": "INSERT", "lineNumber": 93, "content": "  \"activeShipments\": \"الشحنات النشطة\","}, {"type": "INSERT", "lineNumber": 94, "content": "  \"myReceivedShipments\": \"شحناتي المستلمة\","}, {"type": "INSERT", "lineNumber": 95, "content": "  \"mySentShipments\": \"شحناتي المرسلة\","}, {"type": "INSERT", "lineNumber": 96, "content": "  \"noReceivedShipments\": \"لا توجد شحنات مستلمة\","}, {"type": "INSERT", "lineNumber": 97, "content": "  \"noSentShipments\": \"لا توجد شحنات مرسلة\","}, {"type": "INSERT", "lineNumber": 98, "content": "  \"dataLoadError\": \"خطأ في تحميل البيانات\","}, {"type": "INSERT", "lineNumber": 99, "content": "  \"from\": \"من: \","}, {"type": "INSERT", "lineNumber": 100, "content": "  \"to\": \"إلى: \","}, {"type": "INSERT", "lineNumber": 101, "content": "  \"price\": \"السعر: \","}, {"type": "INSERT", "lineNumber": 102, "content": "  \"points\": \"نقطة\","}, {"type": "INSERT", "lineNumber": 103, "content": "  \"homePage\": \"الصفحة الرئيسية\","}, {"type": "INSERT", "lineNumber": 104, "content": "  \"shipmentsHistory\": \"سجل الشحنات\","}, {"type": "INSERT", "lineNumber": 105, "content": "  \"wallet\": \"المحفظة\","}, {"type": "INSERT", "lineNumber": 106, "content": "  \"menu\": \"المنيو\","}, {"type": "INSERT", "lineNumber": 107, "content": "  \"comingSoon\": \"قريباً...\","}, {"type": "INSERT", "lineNumber": 108, "content": "  \"pageUnderDevelopment\": \"هذه الصفحة قيد التطوير\","}, {"type": "INSERT", "lineNumber": 109, "content": "  \"ahmadAli\": \"أحم<PERSON> علي\""}, {"type": "INSERT", "lineNumber": 110, "content": "}"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"التسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الهاتف المحمول\",\n  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"الريلز\",\n  \"shops\": \"المتاجر\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحباً، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\",\n  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\",\n  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\",\n  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\",\n  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\",\n  \"next\": \"التالي\",\n  \"startNow\": \"ابدأ الآن\",\n  \"description\": \"الوصف\",\n  \"address\": \"العنوان\",\n  \"enter\": \"أدخل\",\n  \"doctorName\": \"اسم الطبيب\",\n  \"emailOptional\": \"البريد الإلكتروني (اختياري)\",\n  \"doctorLogo\": \"شعار الطبيب\",\n  \"save\": \"حفظ\",\n  \"submit\": \"إرسال\",\n  \"pickImage\": \"اختيار صورة\",\n  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\",\n  \"pickLocation\": \"اختيار الموقع\",\n  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\",\n  \"changeLocation\": \"تغيير الموقع\",\n  \"noDataFound\": \"لم يتم العثور على بيانات\",\n  \"changeSocial\": \"تغيير وسائل التواصل\",\n  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\",\n  \"socialMedia\": \"وسائل التواصل الاجتماعي\",\n  \"doctorBackground\": \"خلفية الطبيب\",\n  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\",\n  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\",\n  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\",\n  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\",\n  \"storeLogo\": \"شعار المتجر\",\n  \"storeBackground\": \"خلفية المتجر\",\n  \"storeName\": \"اسم المتجر\",\n  \"search\": \"البحث\",\n  \"searchForProducts\": \"البحث عن المنتجات\",\n  \"searchForStores\": \"البحث عن المتاجر\",\n  \"searchForDoctors\": \"البحث عن الأطباء\"\n}", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"التسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"الريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "DELETE", "lineNumber": 31, "oldContent": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"next\": \"التالي\","}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"description\": \"الوصف\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"enter\": \"أدخل\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"save\": \"حفظ\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"submit\": \"إرسال\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"pickImage\": \"اختيار صورة\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"storeName\": \"اسم المتجر\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"search\": \"البح<PERSON>\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "INSERT", "lineNumber": 1, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart", "baseContent": "import 'dart:io';\n\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/app.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nvoid main() async {\n  WidgetsFlutterBinding.ensureInitialized();\n\n  await GetStorageService.init();\n\n  HttpOverrides.global = MyHttpOverrides();\n\n  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);\n\n  runApp(const ProviderScope(child: BaseApp()));\n}\n", "baseTimestamp": 1756931861026, "deltas": [{"timestamp": 1756969419037, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "       Assets."}, {"type": "MODIFY", "lineNumber": 66, "content": "        Assets.SafeArea(", "oldContent": "        SafeArea("}]}, {"timestamp": 1756969431943, "changes": [{"type": "MODIFY", "lineNumber": 66, "content": "       Assets.images.topClipper.image(),", "oldContent": "        Assets.SafeArea("}, {"type": "INSERT", "lineNumber": 67, "content": "        SafeArea("}]}, {"timestamp": 1756969437234, "changes": [{"type": "INSERT", "lineNumber": 5, "content": "import 'generated/assets.gen.dart';"}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "DELETE", "lineNumber": 368, "oldContent": "//"}]}, {"timestamp": 1756969464653, "changes": [{"type": "MODIFY", "lineNumber": 7, "content": "void main() {", "oldContent": "void main() {"}, {"type": "MODIFY", "lineNumber": 68, "content": "        Assets.images.topClipper.image(),", "oldContent": "       Assets.images.topClipper.image(),"}]}, {"timestamp": 1756969475728, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "import 'package:flutter/gestures.dart';", "oldContent": "import 'dart:io';"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "void main() async {"}, {"type": "INSERT", "lineNumber": 2, "content": "import 'package:flutter/services.dart';"}, {"type": "INSERT", "lineNumber": 3, "content": "import 'package:google_fonts/google_fonts.dart';"}, {"type": "INSERT", "lineNumber": 4, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": "  runApp(const MyApp());"}, {"type": "INSERT", "lineNumber": 9, "content": "}"}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 12, "content": "  const MyApp({super.key});"}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "INSERT", "lineNumber": 14, "content": "  @override"}, {"type": "INSERT", "lineNumber": 15, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 16, "content": "    return MaterialApp("}, {"type": "INSERT", "lineNumber": 17, "content": "      title: 'Flutter Sign Up UI',"}, {"type": "INSERT", "lineNumber": 18, "content": "      theme: Theme<PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 19, "content": "        primarySwatch: Colors.green,"}, {"type": "INSERT", "lineNumber": 20, "content": "        textTheme: GoogleFonts.cairoTextTheme(Theme.of(context).textTheme),"}, {"type": "INSERT", "lineNumber": 21, "content": "        scaffoldBackgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 22, "content": "      ),"}, {"type": "INSERT", "lineNumber": 23, "content": "      debugShowCheckedModeBanner: false,"}, {"type": "INSERT", "lineNumber": 24, "content": "      home: const SignUpScreen(),"}, {"type": "INSERT", "lineNumber": 25, "content": "    );"}, {"type": "INSERT", "lineNumber": 26, "content": "  }"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    return false;"}, {"type": "INSERT", "lineNumber": 29, "content": "class SignUpScreen extends StatefulWidget {"}, {"type": "INSERT", "lineNumber": 30, "content": "  const SignUpScreen({super.key});"}, {"type": "INSERT", "lineNumber": 31, "content": ""}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    path.close();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "      size.width,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "      size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "      size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "      size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 33, "content": "  State<SignUpScreen> createState() => _SignUpScreenState();"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 48, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 36, "content": "class _SignUpScreenState extends State<SignUpScreen> {"}, {"type": "INSERT", "lineNumber": 37, "content": "  bool _passwordVisible = false;"}, {"type": "INSERT", "lineNumber": 38, "content": "  bool _confirmPasswordVisible = false;"}, {"type": "INSERT", "lineNumber": 39, "content": "  bool _termsAccepted = false;"}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "  @override"}, {"type": "INSERT", "lineNumber": 42, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 43, "content": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "INSERT", "lineNumber": 44, "content": "      value: SystemUiOverlayStyle.light.copyWith("}, {"type": "INSERT", "lineNumber": 45, "content": "        statusBarColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 46, "content": "        systemNavigationBarColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 47, "content": "        systemNavigationBarIconBrightness: Brightness.dark,"}, {"type": "INSERT", "lineNumber": 48, "content": "      ),"}, {"type": "INSERT", "lineNumber": 49, "content": "      child: Directionality("}, {"type": "INSERT", "lineNumber": 50, "content": "        textDirection: TextDirection.rtl,"}, {"type": "INSERT", "lineNumber": 51, "content": "        child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 52, "content": "          body: SingleChildScrollView("}, {"type": "INSERT", "lineNumber": 53, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 54, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 55, "content": "                _buildHeader(context),"}, {"type": "INSERT", "lineNumber": 56, "content": "                _buildForm(context),"}, {"type": "INSERT", "lineNumber": 57, "content": "              ],"}, {"type": "INSERT", "lineNumber": 58, "content": "            ),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 60, "content": "        ),"}, {"type": "INSERT", "lineNumber": 61, "content": "      ),"}, {"type": "INSERT", "lineNumber": 62, "content": "    );"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                TextSpan("}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        Assets.images.topClipper.image(),"}, {"type": "INSERT", "lineNumber": 64, "content": ""}, {"type": "INSERT", "lineNumber": 65, "content": "  Widget _buildHeader(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 66, "content": "    return Stack("}, {"type": "INSERT", "lineNumber": 67, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 68, "content": "        Assets.images.topClipper.image("}, {"type": "INSERT", "lineNumber": 69, "content": "          width: double.infinity,"}, {"type": "INSERT", "lineNumber": 70, "content": "          height: 200,"}, {"type": "INSERT", "lineNumber": 71, "content": "        ),"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "        SizedBox("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "MODIFY", "lineNumber": 76, "content": "            child: Row(", "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 77, "content": "              mainAxisAlignment: MainAxisAlignment.spaceBetween,"}, {"type": "INSERT", "lineNumber": 78, "content": "              crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 79, "content": "              children: ["}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                    icon: Icon("}, {"type": "DELETE", "lineNumber": 99, "oldContent": "            suffixIcon: isPassword"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "            prefixIconConstraints:"}, {"type": "INSERT", "lineNumber": 101, "content": "                    );"}, {"type": "INSERT", "lineNumber": 102, "content": "                  },"}, {"type": "INSERT", "lineNumber": 103, "content": "                ),"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "//     return false;"}, {"type": "MODIFY", "lineNumber": 216, "content": "                      color: Colors.grey,", "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 217, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 218, "content": "                    onPressed: onToggleVisibility,"}, {"type": "INSERT", "lineNumber": 219, "content": "                  )"}, {"type": "INSERT", "lineNumber": 220, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 221, "content": "            enabledBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 222, "content": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "INSERT", "lineNumber": 223, "content": "            ),"}, {"type": "INSERT", "lineNumber": 224, "content": "            focusedBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 225, "content": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "INSERT", "lineNumber": 226, "content": "            ),"}, {"type": "INSERT", "lineNumber": 227, "content": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "INSERT", "lineNumber": 228, "content": "          ),"}, {"type": "INSERT", "lineNumber": 229, "content": "        ),"}, {"type": "INSERT", "lineNumber": 230, "content": "      ],"}, {"type": "INSERT", "lineNumber": 231, "content": "    );"}, {"type": "INSERT", "lineNumber": 232, "content": "  }"}, {"type": "INSERT", "lineNumber": 233, "content": ""}, {"type": "INSERT", "lineNumber": 234, "content": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 235, "content": "    return Row("}, {"type": "INSERT", "lineNumber": 236, "content": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "INSERT", "lineNumber": 237, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 238, "content": "        SizedBox("}, {"type": "INSERT", "lineNumber": 239, "content": "          width: 24,"}, {"type": "INSERT", "lineNumber": 240, "content": "          height: 24,"}, {"type": "INSERT", "lineNumber": 241, "content": "          child: Checkbox("}, {"type": "INSERT", "lineNumber": 242, "content": "            value: _termsAccepted,"}, {"type": "INSERT", "lineNumber": 243, "content": "            onChanged: (bool? value) {"}, {"type": "INSERT", "lineNumber": 244, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 245, "content": "                _termsAccepted = value ?? false;"}, {"type": "INSERT", "lineNumber": 246, "content": "              });"}, {"type": "INSERT", "lineNumber": 247, "content": "            },"}, {"type": "INSERT", "lineNumber": 248, "content": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 249, "content": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "INSERT", "lineNumber": 250, "content": "            shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 251, "content": "              borderRadius: BorderRadius.circular(4),"}, {"type": "INSERT", "lineNumber": 252, "content": "            ),"}, {"type": "INSERT", "lineNumber": 253, "content": "          ),"}, {"type": "INSERT", "lineNumber": 254, "content": "        ),"}, {"type": "INSERT", "lineNumber": 255, "content": "        const SizedBox(width: 12),"}, {"type": "INSERT", "lineNumber": 256, "content": "        Expanded("}, {"type": "INSERT", "lineNumber": 257, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 258, "content": "            text: TextSpan("}, {"type": "INSERT", "lineNumber": 259, "content": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 260, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 261, "content": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "INSERT", "lineNumber": 262, "content": "                TextSpan("}, {"type": "INSERT", "lineNumber": 263, "content": "                  text: 'الشروط والأحكام',"}, {"type": "INSERT", "lineNumber": 264, "content": "                  style: const TextStyle("}, {"type": "INSERT", "lineNumber": 265, "content": "                    color: Color(0xFF3366CC),"}, {"type": "INSERT", "lineNumber": 266, "content": "                    decoration: TextDecoration.underline,"}, {"type": "INSERT", "lineNumber": 267, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 268, "content": "                  recognizer: TapGestureRecognizer()"}, {"type": "INSERT", "lineNumber": 269, "content": "                    ..onTap = () {"}, {"type": "INSERT", "lineNumber": 270, "content": "                      // Handle terms and conditions tap"}, {"type": "INSERT", "lineNumber": 271, "content": "                    },"}, {"type": "INSERT", "lineNumber": 272, "content": "                ),"}, {"type": "INSERT", "lineNumber": 273, "content": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "INSERT", "lineNumber": 274, "content": "              ],"}, {"type": "INSERT", "lineNumber": 275, "content": "            ),"}, {"type": "INSERT", "lineNumber": 276, "content": "          ),"}, {"type": "INSERT", "lineNumber": 277, "content": "        ),"}, {"type": "INSERT", "lineNumber": 278, "content": "      ],"}, {"type": "INSERT", "lineNumber": 279, "content": "    );"}, {"type": "INSERT", "lineNumber": 280, "content": "  }"}, {"type": "INSERT", "lineNumber": 281, "content": ""}, {"type": "INSERT", "lineNumber": 282, "content": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 283, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 284, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 285, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 286, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 287, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 288, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 289, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 290, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 291, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 292, "content": "          ),"}, {"type": "INSERT", "lineNumber": 293, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 294, "content": "        ),"}, {"type": "INSERT", "lineNumber": 295, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 296, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 297, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 298, "content": "        ),"}, {"type": "INSERT", "lineNumber": 299, "content": "      ),"}, {"type": "INSERT", "lineNumber": 300, "content": "    );"}, {"type": "INSERT", "lineNumber": 301, "content": "  }"}, {"type": "INSERT", "lineNumber": 302, "content": ""}, {"type": "INSERT", "lineNumber": 303, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 304, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 305, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 306, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 307, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 308, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 309, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 310, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "INSERT", "lineNumber": 311, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 313, "content": "          ),"}, {"type": "INSERT", "lineNumber": 314, "content": "        ),"}, {"type": "INSERT", "lineNumber": 315, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 316, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 317, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 318, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 319, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 320, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 321, "content": "          ),"}, {"type": "INSERT", "lineNumber": 322, "content": "        ),"}, {"type": "INSERT", "lineNumber": 323, "content": "      ),"}, {"type": "INSERT", "lineNumber": 324, "content": "    );"}, {"type": "INSERT", "lineNumber": 325, "content": "  }"}, {"type": "INSERT", "lineNumber": 326, "content": "}"}, {"type": "INSERT", "lineNumber": 327, "content": ""}, {"type": "INSERT", "lineNumber": 328, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 226, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 330, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 331, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 332, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "//       size.height,"}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 338, "content": "//     );"}, {"type": "DELETE", "lineNumber": 235, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 340, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 344, "content": "//     );"}, {"type": "INSERT", "lineNumber": 345, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 347, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "INSERT", "lineNumber": 354, "content": "// }"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 356, "content": "//"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "//"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 363, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 364, "content": "//"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969481991, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "import 'package:flutter/material.dart';", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 6, "content": "", "oldContent": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": "void main() async {"}, {"type": "MODIFY", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {", "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 26, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 27, "content": "}"}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "INSERT", "lineNumber": 32, "content": "  @override"}, {"type": "MODIFY", "lineNumber": 34, "content": "}", "oldContent": "  Widget _buildTextField({"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "DELETE", "lineNumber": 47, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "              keyboardType: TextInputType.number),"}, {"type": "MODIFY", "lineNumber": 59, "content": "          ),", "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 63, "content": "  }"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "          child: Padding("}, {"type": "INSERT", "lineNumber": 71, "content": "          fit: BoxFit.cover,"}, {"type": "INSERT", "lineNumber": 73, "content": "        SafeArea("}, {"type": "INSERT", "lineNumber": 74, "content": "          child: Padding("}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "INSERT", "lineNumber": 81, "content": "                const Padding("}, {"type": "INSERT", "lineNumber": 82, "content": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            value: _termsAccepted,"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "          width: 24,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    return Row("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "      ],"}, {"type": "INSERT", "lineNumber": 105, "content": "              ],"}, {"type": "INSERT", "lineNumber": 106, "content": "            ),"}, {"type": "INSERT", "lineNumber": 107, "content": "          ),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "            focusedBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 119, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "                          ? Icons.visibility_off_outlined"}, {"type": "INSERT", "lineNumber": 109, "content": "      ],"}, {"type": "INSERT", "lineNumber": 110, "content": "    );"}, {"type": "INSERT", "lineNumber": 111, "content": "  }"}, {"type": "INSERT", "lineNumber": 112, "content": ""}, {"type": "INSERT", "lineNumber": 113, "content": "  Widget _buildForm(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 114, "content": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 115, "content": ""}, {"type": "INSERT", "lineNumber": 116, "content": "    return Padding("}, {"type": "INSERT", "lineNumber": 117, "content": "      padding: const EdgeInsets.symmetric(horizontal: 32.0),"}, {"type": "INSERT", "lineNumber": 118, "content": "      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 119, "content": "        crossAxisAlignment: CrossAxisAlignment.stretch,"}, {"type": "INSERT", "lineNumber": 120, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 121, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 122, "content": "          _buildTextField(label: 'الاسم', hint: 'الاسم'),"}, {"type": "INSERT", "lineNumber": 123, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 124, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 125, "content": "            label: 'رقم الهاتف:',"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 127, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "          decoration: InputDecoration("}, {"type": "DELETE", "lineNumber": 129, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "          obscureText: obscureText,"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 127, "content": "            keyboardType: TextInputType.phone,"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "            fontSize: 16,"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 135, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 137, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 138, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 140, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "  }) {"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "    bool obscureText = false,"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "    bool isPassword = false,"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "    Widget? prefix,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "    required String hint,"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "    required String label,"}, {"type": "INSERT", "lineNumber": 129, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 130, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 131, "content": "            label: 'كلمة المرور',"}, {"type": "INSERT", "lineNumber": 132, "content": "            hint: 'كلمة المرور',"}, {"type": "INSERT", "lineNumber": 133, "content": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 134, "content": "            obscureText: !_passwordVisible,"}, {"type": "INSERT", "lineNumber": 135, "content": "            onToggleVisibility: () {"}, {"type": "INSERT", "lineNumber": 136, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 137, "content": "                _passwordVisible = !_passwordVisible;"}, {"type": "INSERT", "lineNumber": 143, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 144, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 145, "content": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 146, "content": "            obscureText: !_confirmPasswordVisible,"}, {"type": "INSERT", "lineNumber": 147, "content": "            onToggleVisibility: () {"}, {"type": "INSERT", "lineNumber": 148, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 149, "content": "                _confirmPasswordVisible = !_confirmPasswordVisible;"}, {"type": "INSERT", "lineNumber": 150, "content": "              });"}, {"type": "INSERT", "lineNumber": 151, "content": "            },"}, {"type": "INSERT", "lineNumber": 152, "content": "          ),"}, {"type": "INSERT", "lineNumber": 153, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 154, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                    child: prefix)"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 165, "oldContent": "            prefixIcon: prefix != null"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "          keyboardType: keyboardType,"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "        TextField("}, {"type": "DELETE", "lineNumber": 170, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "            color: Colors.black,"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "          style: const TextStyle("}, {"type": "INSERT", "lineNumber": 164, "content": "            'تملك حساب؟',"}, {"type": "INSERT", "lineNumber": 165, "content": "            textAlign: TextAlign.center,"}, {"type": "INSERT", "lineNumber": 166, "content": "            style: TextStyle(color: Colors.black54, fontSize: 14),"}, {"type": "INSERT", "lineNumber": 167, "content": "          ),"}, {"type": "INSERT", "lineNumber": 168, "content": "          const SizedBox(height: 10),"}, {"type": "INSERT", "lineNumber": 169, "content": "          _build<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(primaryGreen),"}, {"type": "INSERT", "lineNumber": 170, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 171, "content": "        ],"}, {"type": "INSERT", "lineNumber": 172, "content": "      ),"}, {"type": "INSERT", "lineNumber": 173, "content": "    );"}, {"type": "INSERT", "lineNumber": 174, "content": "  }"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 183, "content": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "//       size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 213, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 193, "content": "            fontSize: 16,"}, {"type": "INSERT", "lineNumber": 194, "content": "            color: Colors.black,"}, {"type": "INSERT", "lineNumber": 195, "content": "          ),"}, {"type": "INSERT", "lineNumber": 196, "content": "        ),"}, {"type": "INSERT", "lineNumber": 197, "content": "        TextField("}, {"type": "INSERT", "lineNumber": 198, "content": "          obscureText: obscureText,"}, {"type": "INSERT", "lineNumber": 199, "content": "          keyboardType: keyboardType,"}, {"type": "INSERT", "lineNumber": 200, "content": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 201, "content": "          decoration: InputDecoration("}, {"type": "INSERT", "lineNumber": 202, "content": "            hintText: hint,"}, {"type": "INSERT", "lineNumber": 203, "content": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "INSERT", "lineNumber": 204, "content": "            prefixIcon: prefix != null"}, {"type": "INSERT", "lineNumber": 205, "content": "                ? Padding("}, {"type": "INSERT", "lineNumber": 206, "content": "                    padding: const EdgeInsets.only(top: 12.0, left: 8.0),"}, {"type": "INSERT", "lineNumber": 207, "content": "                    child: prefix)"}, {"type": "INSERT", "lineNumber": 208, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 209, "content": "            prefixIconConstraints:"}, {"type": "INSERT", "lineNumber": 210, "content": "                const BoxConstraints(minWidth: 0, minHeight: 0),"}, {"type": "INSERT", "lineNumber": 211, "content": "            suffixIcon: isPassword"}, {"type": "INSERT", "lineNumber": 212, "content": "                ? IconButton("}, {"type": "INSERT", "lineNumber": 213, "content": "                    icon: Icon("}, {"type": "INSERT", "lineNumber": 214, "content": "                      obscureText"}, {"type": "INSERT", "lineNumber": 215, "content": "                          ? Icons.visibility_off_outlined"}, {"type": "INSERT", "lineNumber": 216, "content": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 258, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 260, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 266, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "//   Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 274, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 276, "oldContent": ""}, {"type": "DELETE", "lineNumber": 278, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 282, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 286, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 288, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 292, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "          style: TextStyle("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "        child: Text("}, {"type": "DELETE", "lineNumber": 302, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 306, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 308, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 310, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 312, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "DELETE", "lineNumber": 314, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 316, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 318, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 320, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 324, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 327, "oldContent": ""}, {"type": "DELETE", "lineNumber": 328, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 330, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 332, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 336, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 342, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 292, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 294, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 295, "content": "        ),"}, {"type": "INSERT", "lineNumber": 296, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 297, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 298, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 299, "content": "        ),"}, {"type": "INSERT", "lineNumber": 300, "content": "      ),"}, {"type": "INSERT", "lineNumber": 301, "content": "    );"}, {"type": "INSERT", "lineNumber": 302, "content": "  }"}, {"type": "INSERT", "lineNumber": 303, "content": ""}, {"type": "INSERT", "lineNumber": 304, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 305, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 306, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 307, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 308, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 309, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 310, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 311, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "INSERT", "lineNumber": 312, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 314, "content": "          ),"}, {"type": "INSERT", "lineNumber": 315, "content": "        ),"}, {"type": "INSERT", "lineNumber": 316, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 317, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 318, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 319, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 320, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 321, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 322, "content": "          ),"}, {"type": "INSERT", "lineNumber": 323, "content": "        ),"}, {"type": "INSERT", "lineNumber": 324, "content": "      ),"}, {"type": "INSERT", "lineNumber": 325, "content": "    );"}, {"type": "INSERT", "lineNumber": 326, "content": "  }"}, {"type": "INSERT", "lineNumber": 327, "content": "}"}, {"type": "INSERT", "lineNumber": 328, "content": ""}, {"type": "INSERT", "lineNumber": 329, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 330, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 331, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 332, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 333, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 334, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 338, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 339, "content": "//     );"}, {"type": "INSERT", "lineNumber": 340, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 344, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 345, "content": "//     );"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 347, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 348, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 349, "content": "//   }"}, {"type": "INSERT", "lineNumber": 350, "content": "//"}, {"type": "INSERT", "lineNumber": 351, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 352, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 353, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 354, "content": "//   }"}, {"type": "INSERT", "lineNumber": 355, "content": "// }"}, {"type": "INSERT", "lineNumber": 356, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 357, "content": "//"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "//"}, {"type": "INSERT", "lineNumber": 363, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 364, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 365, "content": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 367, "content": "//"}, {"type": "INSERT", "lineNumber": 368, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 369, "content": "//"}, {"type": "INSERT", "lineNumber": 370, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 371, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 372, "content": "// }"}, {"type": "INSERT", "lineNumber": 373, "content": ""}]}, {"timestamp": 1756969486228, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 24, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 26, "oldContent": ""}, {"type": "INSERT", "lineNumber": 27, "content": "}"}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "MODIFY", "lineNumber": 32, "content": "  @override", "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 58, "content": "            ),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                : null,"}, {"type": "MODIFY", "lineNumber": 63, "content": "  }", "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "          height: 200,"}, {"type": "MODIFY", "lineNumber": 72, "content": "        SafeArea(", "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 76, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 74, "content": "            padding:"}, {"type": "INSERT", "lineNumber": 75, "content": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),"}, {"type": "MODIFY", "lineNumber": 80, "content": "                const Padding(", "oldContent": "                const Padding("}, {"type": "MODIFY", "lineNumber": 104, "content": "              ],", "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 107, "content": "        ),"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                    child: prefix)"}, {"type": "INSERT", "lineNumber": 127, "content": "          ),"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "      children: ["}, {"type": "INSERT", "lineNumber": 137, "content": "              });"}, {"type": "INSERT", "lineNumber": 138, "content": "            },"}, {"type": "INSERT", "lineNumber": 139, "content": "          ),"}, {"type": "INSERT", "lineNumber": 140, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 141, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 145, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 147, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 151, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 155, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 156, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 157, "oldContent": ""}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 160, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 154, "content": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 155, "content": "              hint: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 156, "content": "              keyboardType: TextInputType.number),"}, {"type": "INSERT", "lineNumber": 157, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 158, "content": "          _buildTermsAndConditions(),"}, {"type": "INSERT", "lineNumber": 159, "content": "          const SizedBox(height: 30),"}, {"type": "INSERT", "lineNumber": 160, "content": "          _buildSignUpButton(),"}, {"type": "INSERT", "lineNumber": 161, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 162, "content": "          const Text("}, {"type": "DELETE", "lineNumber": 175, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 181, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 174, "content": ""}, {"type": "INSERT", "lineNumber": 175, "content": "  Widget _buildTextField({"}, {"type": "INSERT", "lineNumber": 176, "content": "    required String label,"}, {"type": "INSERT", "lineNumber": 177, "content": "    required String hint,"}, {"type": "INSERT", "lineNumber": 178, "content": "    Widget? prefix,"}, {"type": "INSERT", "lineNumber": 179, "content": "    bool isPassword = false,"}, {"type": "INSERT", "lineNumber": 180, "content": "    bool obscureText = false,"}, {"type": "INSERT", "lineNumber": 181, "content": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 187, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 184, "content": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 185, "content": "    return Column("}, {"type": "INSERT", "lineNumber": 186, "content": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 187, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 188, "content": "        Text("}, {"type": "DELETE", "lineNumber": 191, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 190, "content": "          style: const TextStyle("}, {"type": "INSERT", "lineNumber": 191, "content": "            fontWeight: FontWeight.bold,"}, {"type": "MODIFY", "lineNumber": 265, "content": "                    color: Color(0xFF3366CC),", "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 266, "content": "                    decoration: TextDecoration.underline,"}, {"type": "INSERT", "lineNumber": 267, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 268, "content": "                  recognizer: TapGestureRecognizer()"}, {"type": "INSERT", "lineNumber": 269, "content": "                    ..onTap = () {"}, {"type": "INSERT", "lineNumber": 270, "content": "                      // Handle terms and conditions tap"}, {"type": "INSERT", "lineNumber": 271, "content": "                    },"}, {"type": "INSERT", "lineNumber": 272, "content": "                ),"}, {"type": "INSERT", "lineNumber": 273, "content": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "INSERT", "lineNumber": 274, "content": "              ],"}, {"type": "INSERT", "lineNumber": 275, "content": "            ),"}, {"type": "INSERT", "lineNumber": 276, "content": "          ),"}, {"type": "INSERT", "lineNumber": 277, "content": "        ),"}, {"type": "INSERT", "lineNumber": 278, "content": "      ],"}, {"type": "INSERT", "lineNumber": 279, "content": "    );"}, {"type": "INSERT", "lineNumber": 280, "content": "  }"}, {"type": "INSERT", "lineNumber": 281, "content": ""}, {"type": "INSERT", "lineNumber": 282, "content": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 283, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 284, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 285, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 286, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 287, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 288, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 289, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 290, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 291, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 292, "content": "          ),"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                    color: Color(0xFF3366CC),"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 275, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 281, "oldContent": "                    ..onTap = () {"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 287, "oldContent": "                    },"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 293, "oldContent": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 298, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 307, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 310, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 313, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 331, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 337, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 343, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 345, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "INSERT", "lineNumber": 350, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "INSERT", "lineNumber": 354, "content": "// }"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 356, "content": "//"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "//"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 363, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 364, "content": "//"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969526756, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 25, "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 27, "content": "}", "oldContent": ""}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "INSERT", "lineNumber": 31, "content": ""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 62, "content": "    );"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "            padding:"}, {"type": "INSERT", "lineNumber": 72, "content": "        SafeArea("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),"}, {"type": "INSERT", "lineNumber": 74, "content": "            padding:"}, {"type": "INSERT", "lineNumber": 75, "content": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "MODIFY", "lineNumber": 80, "content": "                const Padding(", "oldContent": "                const Padding("}, {"type": "MODIFY", "lineNumber": 104, "content": "              ],", "oldContent": "              ],"}, {"type": "MODIFY", "lineNumber": 107, "content": "        ),", "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 125, "content": "            hint: '05xxxxxxxx',"}, {"type": "INSERT", "lineNumber": 127, "content": "          ),"}, {"type": "MODIFY", "lineNumber": 137, "content": "              });", "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 142, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 143, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 144, "content": "            isPassword: true,"}, {"type": "MODIFY", "lineNumber": 154, "content": "              label: 'رقم الهوية',", "oldContent": "              label: 'رقم الهوية',"}, {"type": "MODIFY", "lineNumber": 174, "content": "", "oldContent": ""}, {"type": "INSERT", "lineNumber": 183, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 189, "content": "          label,"}, {"type": "DELETE", "lineNumber": 190, "oldContent": ""}, {"type": "DELETE", "lineNumber": 257, "oldContent": ""}, {"type": "DELETE", "lineNumber": 258, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 259, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 260, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 261, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 263, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 257, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 258, "content": "            text: TextSpan("}, {"type": "INSERT", "lineNumber": 259, "content": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 260, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 261, "content": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "INSERT", "lineNumber": 262, "content": "                TextSpan("}, {"type": "INSERT", "lineNumber": 263, "content": "                  text: 'الشروط والأحكام',"}, {"type": "INSERT", "lineNumber": 264, "content": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 266, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 273, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 275, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 291, "oldContent": ""}, {"type": "DELETE", "lineNumber": 293, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 303, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 305, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 308, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 293, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 294, "content": "        ),"}, {"type": "INSERT", "lineNumber": 295, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 296, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 297, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 298, "content": "        ),"}, {"type": "INSERT", "lineNumber": 299, "content": "      ),"}, {"type": "INSERT", "lineNumber": 300, "content": "    );"}, {"type": "INSERT", "lineNumber": 301, "content": "  }"}, {"type": "INSERT", "lineNumber": 302, "content": ""}, {"type": "INSERT", "lineNumber": 303, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 304, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 305, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 306, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 307, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 308, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 309, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 326, "content": "}"}, {"type": "INSERT", "lineNumber": 327, "content": ""}, {"type": "INSERT", "lineNumber": 328, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 329, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 330, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 331, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 332, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 333, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 334, "content": "//       size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 338, "content": "//     );"}, {"type": "INSERT", "lineNumber": 339, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 340, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 344, "content": "//     );"}, {"type": "INSERT", "lineNumber": 345, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 347, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "INSERT", "lineNumber": 350, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 329, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 335, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 336, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 337, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 339, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 342, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969537490, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 26, "content": "  }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "            padding:"}, {"type": "INSERT", "lineNumber": 72, "content": "        SafeArea("}, {"type": "MODIFY", "lineNumber": 74, "content": "            padding: const EdgeInsets.symmetric(", "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "INSERT", "lineNumber": 75, "content": "              horizontal: 24.0,"}, {"type": "INSERT", "lineNumber": 76, "content": "            ),"}, {"type": "INSERT", "lineNumber": 80, "content": "              children: ["}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 82, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 83, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 84, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 85, "oldContent": ""}, {"type": "INSERT", "lineNumber": 83, "content": "                  child: Text("}, {"type": "INSERT", "lineNumber": 84, "content": "                    'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 85, "content": "                    style: TextStyle("}, {"type": "INSERT", "lineNumber": 86, "content": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 95, "content": "                  width: 50,"}, {"type": "INSERT", "lineNumber": 96, "content": "                  height: 50,"}, {"type": "INSERT", "lineNumber": 97, "content": "                  errorBuilder: (context, error, stackTrace) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                    return const Icon("}, {"type": "INSERT", "lineNumber": 99, "content": "                      Icons.account_balance, // A placeholder icon"}, {"type": "INSERT", "lineNumber": 100, "content": "                      color: Colors.white,"}, {"type": "INSERT", "lineNumber": 101, "content": "                      size: 50,"}, {"type": "INSERT", "lineNumber": 104, "content": "                ),"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 107, "content": "          ),"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 128, "content": "          ),"}, {"type": "INSERT", "lineNumber": 137, "content": "                _passwordVisible = !_passwordVisible;"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 142, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 143, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 154, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 154, "oldContent": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 174, "content": "  }"}, {"type": "DELETE", "lineNumber": 174, "oldContent": ""}, {"type": "DELETE", "lineNumber": 246, "oldContent": ""}, {"type": "DELETE", "lineNumber": 247, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 251, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 253, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 247, "content": "              });"}, {"type": "INSERT", "lineNumber": 248, "content": "            },"}, {"type": "INSERT", "lineNumber": 249, "content": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 250, "content": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "INSERT", "lineNumber": 251, "content": "            shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 252, "content": "              borderRadius: BorderRadius.circular(4),"}, {"type": "INSERT", "lineNumber": 253, "content": "            ),"}, {"type": "INSERT", "lineNumber": 254, "content": "          ),"}, {"type": "INSERT", "lineNumber": 255, "content": "        ),"}, {"type": "INSERT", "lineNumber": 256, "content": "        const SizedBox(width: 12),"}, {"type": "INSERT", "lineNumber": 257, "content": "        Expanded("}, {"type": "INSERT", "lineNumber": 284, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 285, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 286, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 287, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 288, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 289, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 290, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 291, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 292, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 293, "content": "          ),"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 288, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 293, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "        style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "          foregroundColor: Colors.black,"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 306, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 309, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 316, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 317, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 318, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 320, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 321, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 324, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 316, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 317, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 318, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 319, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 320, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 321, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 322, "content": "          ),"}, {"type": "INSERT", "lineNumber": 323, "content": "        ),"}, {"type": "INSERT", "lineNumber": 324, "content": "      ),"}, {"type": "INSERT", "lineNumber": 325, "content": "    );"}, {"type": "INSERT", "lineNumber": 326, "content": "  }"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 332, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 334, "oldContent": ""}, {"type": "DELETE", "lineNumber": 336, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 347, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 350, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 352, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 356, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 349, "content": "//   }"}, {"type": "INSERT", "lineNumber": 350, "content": "//"}, {"type": "INSERT", "lineNumber": 352, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 353, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 355, "content": "// }"}, {"type": "INSERT", "lineNumber": 356, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "//"}, {"type": "INSERT", "lineNumber": 363, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 364, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 365, "content": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 367, "content": "//"}, {"type": "INSERT", "lineNumber": 368, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 369, "content": "//"}, {"type": "INSERT", "lineNumber": 370, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 371, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 372, "content": "// }"}, {"type": "INSERT", "lineNumber": 373, "content": ""}]}, {"timestamp": 1756969552390, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "MODIFY", "lineNumber": 72, "content": "        SafeArea(", "oldContent": "        SafeArea("}, {"type": "INSERT", "lineNumber": 73, "content": "          child: Padding("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "INSERT", "lineNumber": 76, "content": "              vertical: 16.0,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                const Padding("}, {"type": "INSERT", "lineNumber": 82, "content": "                const Padding("}, {"type": "DELETE", "lineNumber": 87, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 88, "content": "                      fontSize: 32,"}, {"type": "INSERT", "lineNumber": 89, "content": "                      fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 90, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 92, "content": "                ),"}, {"type": "INSERT", "lineNumber": 93, "content": "                // Placeholder for the logo image"}, {"type": "INSERT", "lineNumber": 94, "content": "                Image.asset("}, {"type": "INSERT", "lineNumber": 95, "content": "                  'assets/images/logo_symbol.png',"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 106, "content": "              ],"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 109, "content": "        ),"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              });"}, {"type": "INSERT", "lineNumber": 139, "content": "              });"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 145, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 156, "content": "              label: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 174, "oldContent": ""}, {"type": "INSERT", "lineNumber": 176, "content": ""}, {"type": "DELETE", "lineNumber": 239, "oldContent": ""}, {"type": "DELETE", "lineNumber": 240, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 241, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 242, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 244, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 240, "content": "        SizedBox("}, {"type": "INSERT", "lineNumber": 241, "content": "          width: 24,"}, {"type": "INSERT", "lineNumber": 242, "content": "          height: 24,"}, {"type": "INSERT", "lineNumber": 243, "content": "          child: Checkbox("}, {"type": "INSERT", "lineNumber": 244, "content": "            value: _termsAccepted,"}, {"type": "INSERT", "lineNumber": 245, "content": "            onChanged: (bool? value) {"}, {"type": "INSERT", "lineNumber": 246, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 247, "content": "                _termsAccepted = value ?? false;"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 286, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 295, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 296, "content": "        ),"}, {"type": "INSERT", "lineNumber": 297, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 298, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 299, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 300, "content": "        ),"}, {"type": "INSERT", "lineNumber": 301, "content": "      ),"}, {"type": "DELETE", "lineNumber": 311, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "MODIFY", "lineNumber": 312, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),", "oldContent": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 313, "content": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 314, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "MODIFY", "lineNumber": 315, "content": "          ),", "oldContent": "// void main() async {"}, {"type": "INSERT", "lineNumber": 316, "content": "        ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 346, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 347, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 348, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 349, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 350, "content": "//   }"}, {"type": "INSERT", "lineNumber": 351, "content": "//"}, {"type": "INSERT", "lineNumber": 352, "content": "//   @override"}, {"type": "DELETE", "lineNumber": 350, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 355, "content": "//   }"}, {"type": "DELETE", "lineNumber": 352, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 355, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 358, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 360, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 362, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 364, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 370, "content": "//"}, {"type": "INSERT", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756969554638, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 71, "content": "        ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        SafeArea("}, {"type": "MODIFY", "lineNumber": 76, "content": "              vertical: 8.0,", "oldContent": "              vertical: 16.0,"}, {"type": "MODIFY", "lineNumber": 91, "content": "                  ),", "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 228, "content": "            ),"}, {"type": "INSERT", "lineNumber": 229, "content": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "INSERT", "lineNumber": 230, "content": "          ),"}, {"type": "INSERT", "lineNumber": 231, "content": "        ),"}, {"type": "INSERT", "lineNumber": 232, "content": "      ],"}, {"type": "INSERT", "lineNumber": 233, "content": "    );"}, {"type": "INSERT", "lineNumber": 234, "content": "  }"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 234, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 235, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 236, "content": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 237, "content": "    return Row("}, {"type": "INSERT", "lineNumber": 238, "content": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "INSERT", "lineNumber": 239, "content": "      children: ["}, {"type": "DELETE", "lineNumber": 282, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 282, "content": "  }"}, {"type": "INSERT", "lineNumber": 283, "content": ""}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          elevation: 0,"}, {"type": "MODIFY", "lineNumber": 293, "content": "            borderRadius: BorderRadius.circular(12),", "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 294, "content": "          ),"}, {"type": "INSERT", "lineNumber": 295, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 296, "content": "        ),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 345, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 344, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 345, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 346, "content": "//     );"}, {"type": "DELETE", "lineNumber": 347, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 349, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 351, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 356, "oldContent": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 353, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 354, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 356, "content": "// }"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'dart:io';"}, {"type": "MODIFY", "lineNumber": 360, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';", "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 362, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 363, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 364, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 362, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 364, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 365, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 370, "content": "//"}, {"type": "INSERT", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756969675248, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "MODIFY", "lineNumber": 126, "content": "            label: 'رقم الهاتف',", "oldContent": "            label: 'رقم الهاتف:',"}, {"type": "DELETE", "lineNumber": 221, "oldContent": ""}, {"type": "DELETE", "lineNumber": 222, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 226, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 221, "content": "                  )"}, {"type": "INSERT", "lineNumber": 222, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 223, "content": "            enabledBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 224, "content": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 226, "content": "            focusedBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 227, "content": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "INSERT", "lineNumber": 228, "content": "            ),"}, {"type": "INSERT", "lineNumber": 235, "content": ""}, {"type": "INSERT", "lineNumber": 292, "content": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "MODIFY", "lineNumber": 297, "content": "        child: const Text(", "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 342, "oldContent": ""}, {"type": "DELETE", "lineNumber": 343, "oldContent": "// }"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 351, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "MODIFY", "lineNumber": 353, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {", "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 354, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 355, "content": "//   }"}, {"type": "DELETE", "lineNumber": 357, "oldContent": "//   }"}, {"type": "MODIFY", "lineNumber": 358, "content": "//", "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 360, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "MODIFY", "lineNumber": 361, "content": "// import 'package:dropx/src/app.dart';", "oldContent": "// }"}, {"type": "INSERT", "lineNumber": 363, "content": "//"}, {"type": "DELETE", "lineNumber": 366, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 367, "content": "//   await GetStorageService.init();", "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 369, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "MODIFY", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);", "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756970914590, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "import 'package:flutter/gestures.dart';"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "import 'package:flutter/services.dart';"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "import 'package:google_fonts/google_fonts.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "import 'generated/assets.gen.dart';"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "void main() {"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  runApp(const MyApp());"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  const MyApp({super.key});"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    return MaterialApp("}, {"type": "DELETE", "lineNumber": 17, "oldContent": "      title: 'Flutter Sign Up UI',"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      theme: Theme<PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 19, "oldContent": "        primarySwatch: Colors.green,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "        textTheme: GoogleFonts.cairoTextTheme(Theme.of(context).textTheme),"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "        scaffoldBackgroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      debugShowCheckedModeBanner: false,"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "      home: const SignUpScreen(),"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 28, "oldContent": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "class SignUpScreen extends StatefulWidget {"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  const SignUpScreen({super.key});"}, {"type": "DELETE", "lineNumber": 31, "oldContent": ""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  State<SignUpScreen> createState() => _SignUpScreenState();"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 35, "oldContent": ""}, {"type": "DELETE", "lineNumber": 36, "oldContent": "class _SignUpScreenState extends State<SignUpScreen> {"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  bool _passwordVisible = false;"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  bool _confirmPasswordVisible = false;"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  bool _termsAccepted = false;"}, {"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "      value: SystemUiOverlayStyle.light.copyWith("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "        statusBarColor: const Color(0xFF2DB45D),"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "        systemNavigationBarColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        systemNavigationBarIconBrightness: Brightness.dark,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "      child: Directionality("}, {"type": "DELETE", "lineNumber": 50, "oldContent": "        textDirection: TextDirection.rtl,"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "        child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          body: SingleChildScrollView("}, {"type": "DELETE", "lineNumber": 53, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                _buildHeader(context),"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                _buildForm(context),"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 64, "oldContent": ""}, {"type": "DELETE", "lineNumber": 65, "oldContent": "  Widget _buildHeader(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "    return Stack("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        Assets.images.topClipper.image("}, {"type": "DELETE", "lineNumber": 69, "oldContent": "          width: double.infinity,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "          fit: BoxFit.cover,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "          child: Padding("}, {"type": "DELETE", "lineNumber": 74, "oldContent": "            padding: const EdgeInsets.symmetric("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "              horizontal: 24.0,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "              vertical: 8.0,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "            child: Row("}, {"type": "DELETE", "lineNumber": 79, "oldContent": "              mainAxisAlignment: MainAxisAlignment.spaceBetween,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "              crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 83, "oldContent": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                  child: Text("}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                    'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                    style: TextStyle("}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "                      fontSize: 32,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                // Placeholder for the logo image"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "                Image.asset("}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                  'assets/images/logo_symbol.png',"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                  width: 50,"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "                  height: 50,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                  errorBuilder: (context, error, stackTrace) {"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                    return const Icon("}, {"type": "DELETE", "lineNumber": 100, "oldContent": "                      Icons.account_balance, // A placeholder icon"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                      size: 50,"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                    );"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                  },"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 113, "oldContent": ""}, {"type": "DELETE", "lineNumber": 114, "oldContent": "  Widget _buildForm(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 116, "oldContent": ""}, {"type": "DELETE", "lineNumber": 117, "oldContent": "    return Padding("}, {"type": "DELETE", "lineNumber": 118, "oldContent": "      padding: const EdgeInsets.symmetric(horizontal: 32.0),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "      child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 120, "oldContent": "        crossAxisAlignment: CrossAxisAlignment.stretch,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "        children: ["}, {"type": "DELETE", "lineNumber": 122, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "          _buildTextField(label: 'الاسم', hint: 'الاسم'),"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 126, "oldContent": "            label: 'رقم الهاتف',"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "            hint: '05xxxxxxxx',"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "            keyboardType: TextInputType.phone,"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 132, "oldContent": "            label: 'كلمة المرور',"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "            hint: 'كلمة المرور',"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "            isPassword: true,"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "            obscureText: !_passwordVisible,"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "            onToggleVisibility: () {"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "                _passwordVisible = !_passwordVisible;"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 144, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "            isPassword: true,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "            obscureText: !_confirmPasswordVisible,"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "            onToggleVisibility: () {"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "                _confirmPasswordVisible = !_confirmPasswordVisible;"}, {"type": "DELETE", "lineNumber": 151, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 156, "oldContent": "              label: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "              hint: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "              keyboardType: TextInputType.number),"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "          _buildTermsAndConditions(),"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "          const SizedBox(height: 30),"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "          _buildSignUpButton(),"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "          const Text("}, {"type": "DELETE", "lineNumber": 165, "oldContent": "            'تملك حساب؟',"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "            textAlign: TextAlign.center,"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "            style: TextStyle(color: Colors.black54, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "          const SizedBox(height: 10),"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "          _build<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(primaryGreen),"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 176, "oldContent": ""}, {"type": "DELETE", "lineNumber": 177, "oldContent": "  Widget _buildTextField({"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "    required String label,"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "    required String hint,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "    Widget? prefix,"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "    bool isPassword = false,"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "    bool obscureText = false,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "  }) {"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 187, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 188, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 190, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 191, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 193, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "            fontSize: 16,"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "            color: Colors.black,"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "        TextField("}, {"type": "DELETE", "lineNumber": 199, "oldContent": "          obscureText: obscureText,"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "          keyboardType: keyboardType,"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "          decoration: InputDecoration("}, {"type": "DELETE", "lineNumber": 203, "oldContent": "            hintText: hint,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "            prefixIcon: prefix != null"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "                    padding: const EdgeInsets.only(top: 12.0, left: 8.0),"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "                    child: prefix)"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "            prefixIconConstraints:"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "                const BoxConstraints(minWidth: 0, minHeight: 0),"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "            suffixIcon: isPassword"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "                ? IconButton("}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                    icon: Icon("}, {"type": "DELETE", "lineNumber": 215, "oldContent": "                      obscureText"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "                          ? Icons.visibility_off_outlined"}, {"type": "DELETE", "lineNumber": 217, "oldContent": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 218, "oldContent": ""}, {"type": "DELETE", "lineNumber": 219, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "            enabledBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 224, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "            focusedBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 226, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 228, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 234, "oldContent": ""}, {"type": "DELETE", "lineNumber": 235, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "    return Row("}, {"type": "DELETE", "lineNumber": 238, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 240, "oldContent": "        SizedBox("}, {"type": "DELETE", "lineNumber": 241, "oldContent": "          width: 24,"}, {"type": "DELETE", "lineNumber": 242, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "          child: Checkbox("}, {"type": "DELETE", "lineNumber": 244, "oldContent": "            value: _termsAccepted,"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "            onChanged: (bool? value) {"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 247, "oldContent": "                _termsAccepted = value ?? false;"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "DELETE", "lineNumber": 251, "oldContent": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "            shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 253, "oldContent": "              borderRadius: BorderRadius.circular(4),"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 257, "oldContent": "        const SizedBox(width: 12),"}, {"type": "DELETE", "lineNumber": 258, "oldContent": "        Expanded("}, {"type": "DELETE", "lineNumber": 259, "oldContent": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 260, "oldContent": "            text: TextSpan("}, {"type": "DELETE", "lineNumber": 261, "oldContent": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 263, "oldContent": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "                TextSpan("}, {"type": "DELETE", "lineNumber": 265, "oldContent": "                  text: 'الشروط والأحكام',"}, {"type": "DELETE", "lineNumber": 266, "oldContent": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 267, "oldContent": "                    color: Color(0xFF3366CC),"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 271, "oldContent": "                    ..onTap = () {"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 273, "oldContent": "                    },"}, {"type": "DELETE", "lineNumber": 274, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 275, "oldContent": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "DELETE", "lineNumber": 276, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 277, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 279, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 281, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 282, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 283, "oldContent": ""}, {"type": "DELETE", "lineNumber": 284, "oldContent": "  Widget _buildSignUpButton() {"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 286, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 287, "oldContent": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 288, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "        style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 290, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          foregroundColor: Colors.black,"}, {"type": "DELETE", "lineNumber": 292, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 293, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 297, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 302, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 303, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 304, "oldContent": ""}, {"type": "DELETE", "lineNumber": 305, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 306, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 307, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 308, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 309, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 310, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 311, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "DELETE", "lineNumber": 312, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 313, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 314, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 316, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 317, "oldContent": "        child: Text("}, {"type": "DELETE", "lineNumber": 318, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "          style: TextStyle("}, {"type": "DELETE", "lineNumber": 320, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 321, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 324, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 326, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 329, "oldContent": ""}, {"type": "DELETE", "lineNumber": 341, "oldContent": ""}, {"type": "INSERT", "lineNumber": 13, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 24, "content": "//   @override"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 360, "oldContent": ""}, {"type": "INSERT", "lineNumber": 32, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 366, "oldContent": ""}, {"type": "INSERT", "lineNumber": 38, "content": "//"}, {"type": "INSERT", "lineNumber": 42, "content": "//"}, {"type": "DELETE", "lineNumber": 372, "oldContent": ""}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756971252863, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": ""}, {"type": "DELETE", "lineNumber": 1, "oldContent": ""}, {"type": "DELETE", "lineNumber": 2, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 3, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 22, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 30, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 36, "content": "//"}, {"type": "INSERT", "lineNumber": 40, "content": "//"}, {"type": "INSERT", "lineNumber": 44, "content": ""}]}, {"timestamp": 1756975710176, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "//   Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 4, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 7, "oldContent": "//       size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 2, "content": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 3, "content": "  @override"}, {"type": "INSERT", "lineNumber": 4, "content": "  Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 5, "content": "    final path = Path();"}, {"type": "INSERT", "lineNumber": 6, "content": "    path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 7, "content": "    path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 8, "content": "      size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 9, "content": "      size.height,"}, {"type": "INSERT", "lineNumber": 10, "content": "      size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 11, "content": "      size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 12, "content": "    );"}, {"type": "INSERT", "lineNumber": 13, "content": "    path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 14, "content": "      size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 15, "content": "      size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 16, "content": "      size.width,"}, {"type": "INSERT", "lineNumber": 17, "content": "      size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 18, "content": "    );"}, {"type": "INSERT", "lineNumber": 19, "content": "    path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 20, "content": "    path.close();"}, {"type": "INSERT", "lineNumber": 21, "content": "    return path;"}, {"type": "INSERT", "lineNumber": 22, "content": "  }"}, {"type": "INSERT", "lineNumber": 23, "content": ""}, {"type": "INSERT", "lineNumber": 24, "content": "  @override"}, {"type": "INSERT", "lineNumber": 25, "content": "  bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 26, "content": "    return false;"}, {"type": "INSERT", "lineNumber": 27, "content": "  }"}, {"type": "INSERT", "lineNumber": 28, "content": "}"}, {"type": "INSERT", "lineNumber": 29, "content": "import 'dart:io';"}, {"type": "INSERT", "lineNumber": 30, "content": ""}, {"type": "INSERT", "lineNumber": 31, "content": "import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 32, "content": "import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 33, "content": "import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 34, "content": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "INSERT", "lineNumber": 36, "content": "void main() async {"}, {"type": "INSERT", "lineNumber": 37, "content": "  WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 38, "content": ""}, {"type": "INSERT", "lineNumber": 39, "content": "  await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "  HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 42, "content": ""}, {"type": "INSERT", "lineNumber": 43, "content": "  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 44, "content": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 45, "content": "}"}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756975716396, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "    final path = Path();"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "    path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "    path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 6, "oldContent": "      size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "      size.height,"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "      size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "      size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "    path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 13, "oldContent": "      size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "      size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "      size.width,"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "      size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "    path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "    path.close();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "    return path;"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 22, "oldContent": ""}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    return false;"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 42, "oldContent": ""}, {"type": "DELETE", "lineNumber": 45, "oldContent": ""}, {"type": "INSERT", "lineNumber": 18, "content": ""}]}, {"timestamp": 1756975815193, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": ""}, {"type": "INSERT", "lineNumber": 15, "content": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 16, "content": "}"}, {"type": "INSERT", "lineNumber": 17, "content": ""}]}, {"timestamp": 1757278530933, "changes": [{"type": "INSERT", "lineNumber": 11, "content": "  await GetStorageService.setData(key: 'token', value: '');"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 16, "content": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 17, "content": "}"}]}, {"timestamp": 1757278536708, "changes": [{"type": "MODIFY", "lineNumber": 11, "content": "  await GetStorageService.setData(", "oldContent": "  await GetStorageService.setData(key: 'token', value: '');"}, {"type": "INSERT", "lineNumber": 12, "content": "      key: 'token',"}, {"type": "INSERT", "lineNumber": 13, "content": "      value: '5|UiFBgZExlsv99j4PW65dKbKC3FFX3J0PmIZ60IVT1b31a35d');"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 18, "content": "  runApp(const ProviderScope(child: BaseApp()));"}]}, {"timestamp": 1757278912707, "changes": [{"type": "DELETE", "lineNumber": 11, "oldContent": "  await GetStorageService.setData("}, {"type": "DELETE", "lineNumber": 12, "oldContent": "      key: 'token',"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "      value: '5|UiFBgZExlsv99j4PW65dKbKC3FFX3J0PmIZ60IVT1b31a35d');"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 16, "content": "}"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/register.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/register.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_form_builder/flutter_form_builder.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/main_screen/view/main.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/register_fields_container.widget.dart';\n\nclass RegisterScreen extends HookConsumerWidget {\n  const RegisterScreen({\n    super.key,\n  });\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());\n\n    return Scaffold(\n      appBar: AppBar(\n        backgroundColor: Colors.transparent,\n        surfaceTintColor: Colors.transparent,\n        actions: [\n          TextButton(\n            onPressed: () {\n              const MainScreen().navigate;\n            },\n            child: Row(\n              children: [\n                Text(\n                  context.tr.skip,\n                  style: AppTextStyles.title,\n                ),\n                AppGaps.gap8,\n                const CircleAvatar(\n                  radius: 18,\n                  backgroundColor: ColorManager.primaryColor,\n                  child: Icon(\n                    Icons.arrow_forward_ios,\n                    color: ColorManager.white,\n                  ),\n                ),\n              ],\n            ),\n          ),\n        ],\n      ),\n      body: FormBuilder(\n        key: formKey,\n        child: Padding(\n          padding: const EdgeInsets.all(AppSpaces.padding12),\n          child: ListView(\n            children: [\n              ClipRRect(\n                borderRadius: BorderRadius.circular(AppRadius.radius28),\n                child:\n                    Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 220.w),\n              ).center(),\n              Text(\n                context.tr.registerWithYourAccountNow,\n                style: AppTextStyles.title.copyWith(\n                  fontWeight: FontWeight.bold,\n                ),\n              ).center(),\n              AppGaps.gap24,\n\n              // * Fields Container\n              RegisterFieldsContainer(\n                formKey: formKey,\n              )\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 59, "oldContent": "                child:"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                    Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 220.w),"}, {"type": "INSERT", "lineNumber": 59, "content": "                child: Assets.images.logoSymbol"}, {"type": "INSERT", "lineNumber": 60, "content": "                    .image(fit: BoxFit.cover, width: 220.w),"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "      appBar: AppBar("}, {"type": "DELETE", "lineNumber": 24, "oldContent": "        backgroundColor: Colors.transparent,"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        surfaceTintColor: Colors.transparent,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "        actions: ["}, {"type": "DELETE", "lineNumber": 27, "oldContent": "          TextButton("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "            onPressed: () {"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              const MainScreen().navigate;"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "            child: Row("}, {"type": "DELETE", "lineNumber": 32, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                Text("}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                  context.tr.skip,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                  style: AppTextStyles.title,"}, {"type": "INSERT", "lineNumber": 23, "content": "      body: Column("}, {"type": "INSERT", "lineNumber": 24, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 25, "content": "          // <PERSON>"}, {"type": "INSERT", "lineNumber": 26, "content": "          Container("}, {"type": "INSERT", "lineNumber": 27, "content": "            width: double.infinity,"}, {"type": "INSERT", "lineNumber": 28, "content": "            decoration: const BoxDecoration("}, {"type": "INSERT", "lineNumber": 29, "content": "              color: ColorManager.primaryColor,"}, {"type": "INSERT", "lineNumber": 30, "content": "              borderRadius: BorderRadius.only("}, {"type": "INSERT", "lineNumber": 31, "content": "                bottomLeft: Ra<PERSON>.circular(30),"}, {"type": "INSERT", "lineNumber": 32, "content": "                bottomRight: Radius.circular(30),"}, {"type": "INSERT", "lineNumber": 33, "content": "              ),"}, {"type": "INSERT", "lineNumber": 34, "content": "            ),"}, {"type": "INSERT", "lineNumber": 35, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 36, "content": "              child: Padding("}, {"type": "INSERT", "lineNumber": 37, "content": "                padding: const EdgeInsets.all(AppSpaces.padding24),"}, {"type": "INSERT", "lineNumber": 38, "content": "                child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 39, "content": "                  children: ["}, {"type": "INSERT", "lineNumber": 40, "content": "                    // Logo"}, {"type": "INSERT", "lineNumber": 41, "content": "                    Assets.images.logoSymbol.image("}, {"type": "INSERT", "lineNumber": 42, "content": "                      width: 60.w,"}, {"type": "INSERT", "lineNumber": 43, "content": "                      height: 60.h,"}, {"type": "INSERT", "lineNumber": 44, "content": "                      color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 45, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 46, "content": "                    AppGaps.gap16,"}, {"type": "INSERT", "lineNumber": 47, "content": "                    // Title"}, {"type": "INSERT", "lineNumber": 48, "content": "                    Text("}, {"type": "INSERT", "lineNumber": 49, "content": "                      context.tr.createAccount,"}, {"type": "INSERT", "lineNumber": 50, "content": "                      style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 51, "content": "                        color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 52, "content": "                        fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 53, "content": "                        fontSize: 24.sp,"}, {"type": "INSERT", "lineNumber": 54, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 55, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 56, "content": "                  ],"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                AppGaps.gap8,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                const CircleAvatar("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                  radius: 18,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  backgroundColor: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                  child: <PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    Icons.arrow_forward_ios,"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                    color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                  ),"}, {"type": "INSERT", "lineNumber": 58, "content": "              ),"}, {"type": "INSERT", "lineNumber": 59, "content": "            ),"}, {"type": "INSERT", "lineNumber": 60, "content": "          ),"}, {"type": "INSERT", "lineNumber": 61, "content": ""}, {"type": "INSERT", "lineNumber": 62, "content": "          // Form Content"}, {"type": "INSERT", "lineNumber": 63, "content": "          Expanded("}, {"type": "INSERT", "lineNumber": 64, "content": "            child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 65, "content": "              key: <PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 66, "content": "              child: Padding("}, {"type": "INSERT", "lineNumber": 67, "content": "                padding: const EdgeInsets.all(AppSpaces.padding16),"}, {"type": "INSERT", "lineNumber": 68, "content": "                child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 69, "content": "                  formKey: formKey,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 71, "content": "              ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "      body: FormBuilder("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "        key: <PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        child: Padding("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          padding: const EdgeInsets.all(AppSpaces.padding12),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "            children: ["}, {"type": "DELETE", "lineNumber": 57, "oldContent": "              ClipRRect("}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                borderRadius: BorderRadius.circular(AppRadius.radius28),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                child: Assets.images.logoSymbol"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                    .image(fit: BoxFit.cover, width: 220.w),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "              ).center(),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "              Text("}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                context.tr.registerWithYourAccountNow,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                  fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "              ).center(),"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "              AppGaps.gap24,"}, {"type": "DELETE", "lineNumber": 69, "oldContent": ""}, {"type": "DELETE", "lineNumber": 70, "oldContent": "              // * Fields Container"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "              RegisterFieldsContainer("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                formKey: formKey,"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "              )"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "            ],"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "      ),"}]}, {"timestamp": 1757271868324, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/core/shared/extensions/context_extensions.dart';"}, {"type": "INSERT", "lineNumber": 1, "content": "import 'package:dropx/src/core/shared/widgets/app_bar/base_header.widget.dart';"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "import 'package:flutter_screenutil/flutter_screenutil.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/generated/assets.gen.dart';"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import 'package:dropx/src/core/shared/extensions/context_extensions.dart';"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "import 'package:dropx/src/core/theme/color_manager.dart';"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "import 'package:dropx/src/screens/main_screen/view/main.screen.dart';"}, {"type": "MODIFY", "lineNumber": 8, "content": "import 'widgets/register_header.widget.dart';", "oldContent": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    return Scaffold("}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      body: Column("}, {"type": "DELETE", "lineNumber": 24, "oldContent": "        children: ["}, {"type": "DELETE", "lineNumber": 25, "oldContent": "          // <PERSON>"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "          Container("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "            width: double.infinity,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "            decoration: const BoxDecoration("}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              color: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "              borderRadius: BorderRadius.only("}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                bottomLeft: Ra<PERSON>.circular(30),"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                bottomRight: Radius.circular(30),"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "              child: Padding("}, {"type": "INSERT", "lineNumber": 19, "content": "    return Directionality("}, {"type": "INSERT", "lineNumber": 20, "content": "      textDirection: TextDirection.rtl,"}, {"type": "INSERT", "lineNumber": 21, "content": "      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 22, "content": "        backgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 23, "content": "        body: SingleChildScrollView("}, {"type": "INSERT", "lineNumber": 24, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 25, "content": "            key: <PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 26, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 27, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 28, "content": "                // Header"}, {"type": "INSERT", "lineNumber": 29, "content": "                BaseHeaderWidget("}, {"type": "INSERT", "lineNumber": 30, "content": "                  title: context.tr.createAccount,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                padding: const EdgeInsets.all(AppSpaces.padding24),"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  children: ["}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                    // Logo"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    Assets.images.logoSymbol.image("}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                      width: 60.w,"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                      height: 60.h,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                      color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                    ),"}, {"type": "INSERT", "lineNumber": 32, "content": ""}, {"type": "INSERT", "lineNumber": 33, "content": "                // Form Content"}, {"type": "INSERT", "lineNumber": 34, "content": "                RegisterFieldsContainer("}, {"type": "INSERT", "lineNumber": 35, "content": "                  formKey: formKey,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                    AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                    // Title"}, {"type": "INSERT", "lineNumber": 37, "content": "              ],"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                    Text("}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                      context.tr.createAccount,"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                      style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 40, "content": "        ),"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                        color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                        fontSize: 24.sp,"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                  ],"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 66, "oldContent": ""}, {"type": "DELETE", "lineNumber": 67, "oldContent": "          // Form Content"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          Expanded("}, {"type": "DELETE", "lineNumber": 69, "oldContent": "            child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 70, "oldContent": "              key: <PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "              child: Padding("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                padding: const EdgeInsets.all(AppSpaces.padding16),"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "                child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 74, "oldContent": "                  formKey: formKey,"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "              ),"}]}, {"timestamp": 1757271885214, "changes": [{"type": "MODIFY", "lineNumber": 2, "content": "import 'package:flutter/material.dart';", "oldContent": "import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import 'widgets/register_header.widget.dart';"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    return Directionality("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      textDirection: TextDirection.rtl,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 18, "content": "    return Directionality("}, {"type": "INSERT", "lineNumber": 19, "content": "      textDirection: TextDirection.rtl,"}, {"type": "INSERT", "lineNumber": 20, "content": "      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 30, "content": "                ),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 36, "content": "              ],"}, {"type": "INSERT", "lineNumber": 39, "content": "        ),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/gradle/wrapper/gradle-wrapper.properties": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/gradle/wrapper/gradle-wrapper.properties", "baseContent": "distributionBase=GRADLE_USER_HOME\ndistributionPath=wrapper/dists\nzipStoreBase=GRADLE_USER_HOME\nzipStorePath=wrapper/dists\ndistributionUrl=https\\://services.gradle.org/distributions/gradle-8.10.2-all.zip\n", "baseTimestamp": 1756967820315}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/build.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/build.gradle", "baseContent": "plugins {\n    id \"com.android.application\"\n    id \"kotlin-android\"\n    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.\n    id \"dev.flutter.flutter-gradle-plugin\"\n}\n\nandroid {\n    namespace = \"com.ajory.dropx\"\n    compileSdk = flutter.compileSdkVersion\n    ndkVersion = flutter.ndkVersion\n\n    compileOptions {\n        sourceCompatibility = JavaVersion.VERSION_1_8\n        targetCompatibility = JavaVersion.VERSION_1_8\n    }\n\n    kotlinOptions {\n        jvmTarget = JavaVersion.VERSION_1_8\n    }\n\n    defaultConfig {\n        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).\n        applicationId = \"com.ajory.dropx\"\n        // You can update the following values to match your application needs.\n        // For more information, see: https://flutter.dev/to/review-gradle-config.\n        minSdk = 23\n        targetSdk = flutter.targetSdkVersion\n        versionCode = flutter.versionCode\n        versionName = flutter.versionName\n    }\n\n    buildTypes {\n        release {\n            // TODO: Add your own signing config for the release build.\n            // Signing with the debug keys for now, so `flutter run --release` works.\n            signingConfig = signingConfigs.debug\n        }\n    }\n}\n\nflutter {\n    source = \"../..\"\n}\n", "baseTimestamp": 1756967830121, "deltas": [{"timestamp": 1756967833204, "changes": [{"type": "MODIFY", "lineNumber": 27, "content": "        targetSdk = 36", "oldContent": "        targetSdk = flutter.targetSdkVersion"}]}, {"timestamp": 1756967835944, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "    compileSdk = 36", "oldContent": "    compileSdk = flutter.compileSdkVersion"}]}, {"timestamp": 1756967936981, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "    ndkVersion \"28.0.13004108\"", "oldContent": "    ndkVersion = flutter.ndkVersion"}, {"type": "INSERT", "lineNumber": 12, "content": ""}]}, {"timestamp": 1756967940665, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "    ndkVersion = \"28.0.13004108\"", "oldContent": "    ndkVersion \"28.0.13004108\""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/ios/Podfile": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/ios/Podfile", "baseContent": "# Uncomment this line to define a global platform for your project\nplatform :ios, '13.0'x\n\n# CocoaPods analytics sends network stats synchronously affecting flutter build latency.\nENV['COCOAPODS_DISABLE_STATS'] = 'true'\n\nproject 'Runner', {\n  'Debug' => :debug,\n  'Profile' => :release,\n  'Release' => :release,\n}\n\ndef flutter_root\n  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)\n  unless File.exist?(generated_xcode_build_settings_path)\n    raise \"#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first\"\n  end\n\n  File.foreach(generated_xcode_build_settings_path) do |line|\n    matches = line.match(/FLUTTER_ROOT\\=(.*)/)\n    return matches[1].strip if matches\n  end\n  raise \"FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get\"\nend\n\nrequire File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)\n\nflutter_ios_podfile_setup\n\ntarget 'Runner' do\n  use_frameworks!\n  use_modular_headers!\n\n  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))\n  target 'RunnerTests' do\n    inherit! :search_paths\n  end\nend\n\npost_install do |installer|\n  installer.pods_project.targets.each do |target|\n    flutter_additional_ios_build_settings(target)\n  end\nend\n", "baseTimestamp": 1756968053055, "deltas": [{"timestamp": 1756968055555, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "platform :ios, '13.0'", "oldContent": "platform :ios, '13.0'x"}]}, {"timestamp": 1756968058001, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "platform :ios, '15.0'", "oldContent": "platform :ios, '13.0'"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/controller/settings_controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/controller/settings_controller.dart", "baseContent": "import 'dart:async';\n\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../repository/local_settings_repo.dart';\n\nfinal settingsControllerProvider =\n    ChangeNotifierProvider<AppSettingsController>((ref) {\n  final settingsLocalRepo = ref.watch(settingsRepoProvider);\n  return AppSettingsController(settingsLocalRepo: settingsLocalRepo)\n    ..loadSettings();\n});\n\nclass AppSettingsController extends BaseVM {\n  final SettingsLocalRepo settingsLocalRepo;\n\n  AppSettingsController({required this.settingsLocalRepo});\n\n  //! Load Settings ===================================\n  Future<void> loadSettings() async {\n    _locale = await settingsLocalRepo.locale();\n\n    notifyListeners();\n  }\n\n  Locale _locale = const Locale('ar', 'EG');\n\n  Locale get locale => _locale;\n\n  bool get isEnglish => _locale.languageCode == 'en';\n\n  //! Update Language  ===================================\n  Future<void> updateLanguage(Locale newLocale) async {\n    if (_locale == newLocale) return;\n    _locale = newLocale;\n    await settingsLocalRepo.updateLanguage(newLocale);\n    notifyListeners();\n  }\n}\n", "baseTimestamp": 1756968365516}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/repository/local_settings_repo.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/repository/local_settings_repo.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nfinal settingsRepoProvider = Provider<SettingsLocalRepo>((ref) {\n  return SettingsLocalRepo();\n});\n\nclass SettingsLocalRepo {\n  Future<void> updateLanguage(Locale locale) async {\n    GetStorageService.setData(\n        key: LocalKeys.language, value: locale.languageCode);\n  }\n\n  Future<Locale> locale() async {\n    final langCode = await GetStorageService.getData(key: LocalKeys.language);\n\n    if (langCode != null) {\n      return Locale(langCode);\n    } else {\n      return const Locale('ar', 'EG');\n    }\n  }\n}\n", "baseTimestamp": 1756968372623}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/app.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/app.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:google_fonts/google_fonts.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:dropx/src/screens/main_screen/view/main.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'core/consts/app_constants.dart';\nimport 'core/shared/services/app_settings/controller/settings_controller.dart';\nimport 'core/shared/widgets/loading/loading_widget.dart';\nimport 'core/theme/theme_manager.dart';\n\nclass BaseApp extends HookConsumerWidget {\n  const BaseApp({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final settingsController = ref.watch(settingsControllerProvider);\n\n    ScreenUtil.init(context);\n\n    final theme = AppTheme(\n        appTextTheme: settingsController.isEnglish\n            ? GoogleFonts.workSansTextTheme()\n            : GoogleFonts.cairoTextTheme());\n\n    return BaseMaterialApp(\n      title: AppConsts.appName,\n      //? Localization\n      locale: settingsController.locale,\n      supportedLocales: AppConsts.supportedLocales,\n      localizationsDelegates: AppConsts.localizationsDelegates,\n      //? Theme\n      theme: theme.appTheme(),\n      loadingWidget: const LoadingWidget(),\n      home: \n      // const MainScreen(),\n      const SplashScreen(),\n      // const SplashScreen()\n      // const MainScreen(),\n    );\n  }\n}\n", "baseTimestamp": 1756968401774, "deltas": [{"timestamp": 1756968405309, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/splash/view/splash_screen.dart';"}]}, {"timestamp": 1756968427944, "changes": [{"type": "DELETE", "lineNumber": 36, "oldContent": "      home: "}, {"type": "DELETE", "lineNumber": 37, "oldContent": "      // const MainScreen(),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "      const S<PERSON>lashScreen(),"}, {"type": "INSERT", "lineNumber": 36, "content": "      home:"}, {"type": "INSERT", "lineNumber": 37, "content": "          // const MainScreen(),"}, {"type": "INSERT", "lineNumber": 38, "content": "          const S<PERSON>lashScreen(),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/splash/view/splash_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/splash/view/splash_screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/screens/on_boarding/view/on_boarding.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass SplashScreen extends HookWidget {\n  const SplashScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    final loggedIn = GetStorageService.hasData(key: LocalKeys.user);\n\n    useEffect(() {\n      const navigateWidget =\n      // OnBoardingScreen();\n      // const LoginScreen();\n      // loggedIn ? const MainScreen() : const LoginScreen();\n\n      Future.delayed(const Duration(seconds: 6), () {\n        navigateWidget.navigate;\n      });\n\n      return () {};\n    }, []);\n\n    return Scaffold(\n      body: Center(\n        child: Assets.images.logo.image(fit: BoxFit.cover),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756968413710, "deltas": [{"timestamp": 1756968421665, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "      const navigateWidget =RegisterScreen();", "oldContent": "      const navigateWidget ="}]}, {"timestamp": 1756968427949, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../auth/view/register/register.screen.dart';"}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": "      const navigateWidget =RegisterScreen();"}, {"type": "INSERT", "lineNumber": 16, "content": "      const navigateWidget = RegisterScreen();"}]}, {"timestamp": 1757271680627, "changes": [{"type": "MODIFY", "lineNumber": 8, "content": "class SplashScreen extends HookWidget {", "oldContent": "class SplashScreen extends HookWidget {"}, {"type": "INSERT", "lineNumber": 16, "content": "      const navigateWidget ="}, {"type": "INSERT", "lineNumber": 17, "content": "      // RegisterScreen();"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "      const navigateWidget = RegisterScreen();"}, {"type": "MODIFY", "lineNumber": 20, "content": "      loggedIn ? const MainScreen() : const LoginScreen();", "oldContent": "      // loggedIn ? const MainScreen() : const LoginScreen();"}]}, {"timestamp": 1757271690597, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../auth/view/login/login.screen.dart';"}, {"type": "INSERT", "lineNumber": 8, "content": "import '../../main_screen/view/main.screen.dart';"}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": "class SplashScreen extends HookWidget {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "      const navigateWidget ="}, {"type": "DELETE", "lineNumber": 17, "oldContent": "      // OnBoardingScreen();"}, {"type": "INSERT", "lineNumber": 18, "content": "      final navigateWidget ="}, {"type": "INSERT", "lineNumber": 19, "content": "          loggedIn ? const MainScreen() : const LoginScreen();"}, {"type": "INSERT", "lineNumber": 21, "content": "      // OnBoardingScreen();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      // loggedIn ? const MainScreen() : const LoginScreen();"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      loggedIn ? const MainScreen() : const LoginScreen();"}, {"type": "INSERT", "lineNumber": 23, "content": ""}]}, {"timestamp": 1757271702002, "changes": [{"type": "DELETE", "lineNumber": 8, "oldContent": "class SplashScreen extends HookWidget {"}, {"type": "INSERT", "lineNumber": 10, "content": "class SplashScreen extends HookWidget {"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "      // RegisterScreen();"}, {"type": "MODIFY", "lineNumber": 20, "content": "      // RegisterScreen();", "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}]}, {"timestamp": 1757271706092, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "class SplashScreen extends HookWidget {", "oldContent": "class SplashScreen extends HookWidget {"}, {"type": "INSERT", "lineNumber": 19, "content": "          loggedIn ? const MainScreen() : const LoginScreen();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "MODIFY", "lineNumber": 33, "content": "        child: Assets.images.logoSymbol.image(fit: BoxFit.cover),", "oldContent": "        child: Assets.images.logo.image(fit: BoxFit.cover),"}]}, {"timestamp": 1757271722105, "changes": [{"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": "class SplashScreen extends HookWidget {"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "      body: Center("}, {"type": "DELETE", "lineNumber": 33, "oldContent": "        child: Assets.images.logoSymbol.image(fit: BoxFit.cover),"}, {"type": "INSERT", "lineNumber": 32, "content": "      body: Padding("}, {"type": "INSERT", "lineNumber": 33, "content": "        padding: const EdgeInsets.all(8.0),"}, {"type": "INSERT", "lineNumber": 34, "content": "        child: Center("}, {"type": "INSERT", "lineNumber": 35, "content": "          child: Assets.images.logoSymbol.image(fit: BoxFit.cover),"}, {"type": "INSERT", "lineNumber": 36, "content": "        ),"}]}, {"timestamp": 1757271725995, "changes": [{"type": "DELETE", "lineNumber": 21, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "MODIFY", "lineNumber": 33, "content": "        padding: const EdgeInsets.all(AppS),", "oldContent": "        padding: const EdgeInsets.all(8.0),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "      ),"}, {"type": "MODIFY", "lineNumber": 37, "content": "      ),", "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 38, "content": "    );"}]}, {"timestamp": 1757271728374, "changes": [{"type": "DELETE", "lineNumber": 21, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "MODIFY", "lineNumber": 33, "content": "        padding: const EdgeInsets.all(AppSpaces.),", "oldContent": "        padding: const EdgeInsets.all(AppS),"}, {"type": "INSERT", "lineNumber": 36, "content": "        ),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        ),"}]}, {"timestamp": 1757271730452, "changes": [{"type": "DELETE", "lineNumber": 21, "oldContent": "      // const LoginScreen();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "INSERT", "lineNumber": 22, "content": "      // const LoginScreen();"}, {"type": "INSERT", "lineNumber": 24, "content": "      Future.delayed(const Duration(seconds: 6), () {"}, {"type": "MODIFY", "lineNumber": 33, "content": "        padding: const EdgeInsets.all(AppSpaces.padding24),", "oldContent": "        padding: const EdgeInsets.all(AppSpaces.),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/theme/color_manager.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/theme/color_manager.dart", "baseContent": "import 'package:flutter/material.dart';\n\nclass ColorManager {\n  static const primaryColor = Color(0xFF29b65d);\n  static final lightPrimaryColor = const Color(0xFFf5a926).withOpacity(0.6);\n\n  static const secondaryColor = Color(0xFFECF0FF);\n\n  static const buttonColor = secondaryColor;\n  static final containerColor = Colors.grey.withOpacity(0.1);\n  static const selectedContainerColor = Color(0xFFD3E1E2);\n  static const fieldColor = Color(0xFFCBD5E1);\n  static const white = Color(0xFFFFFFFF);\n  static const black = Color(0xFF000000);\n  static const grey = Color(0xFFf5f5f5);\n  static const greyIcon = Color(0xFF9E9E9E);\n  static const highlightColor = Color(0xFFFFFFFF);\n  static const lightGrey = Color(0xFFEEF1F6);\n\n  static const shimmerBaseColor = Color(0xFFCECECE);\n  static const cardColor = Color(0xFFEDEDED);\n  static const darkGrey = Color(0xFFA4A4A4);\n  static const darkBlue = Color(0xFF23292F);\n  static const iconColor = Color(0xFF727272);\n  static const errorColor = Color(0xFFE74C3C);\n  static const successColor = Color(0xFF2ECC71);\n}\n", "baseTimestamp": 1756975831329, "deltas": [{"timestamp": 1756975833291, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "  static final lightPrimaryColor = const Color(0xFF29b65d).withOpacity(0.6);", "oldContent": "  static final lightPrimaryColor = const Color(0xFFf5a926).withOpacity(0.6);"}]}, {"timestamp": 1756976453563, "changes": [{"type": "DELETE", "lineNumber": 3, "oldContent": "  static const primaryColor = Color(0xFF29b65d);"}, {"type": "MODIFY", "lineNumber": 3, "content": "  static const primaryColor = Color(0xFF2DB45D);", "oldContent": "  static final lightPrimaryColor = const Color(0xFF29b65d).withOpacity(0.6);"}, {"type": "INSERT", "lineNumber": 4, "content": "  static final lightPrimaryColor = const Color(0xFF2DB45D).withOpacity(0.6);"}]}, {"timestamp": 1756976462342, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "  static final lightPrimaryColor = const Color(0xFF2DB45D).withOpacity(0.6);", "oldContent": "  static final lightPrimaryColor = const Color(0xFF29b65d).withOpacity(0.6);"}, {"type": "INSERT", "lineNumber": 26, "content": "  static const disabledButtonColor = Color(0xFFC4C4C4);"}, {"type": "INSERT", "lineNumber": 27, "content": "  static const termsLinkColor = Color(0xFF3366CC);"}]}, {"timestamp": 1756976528759, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "  static final lightPrimaryColor = primaryColor.withOpacity(0.6);", "oldContent": "  static final lightPrimaryColor = const Color(0xFF2DB45D).withOpacity(0.6);"}, {"type": "MODIFY", "lineNumber": 28, "content": "}", "oldContent": "}"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/fields/text_field.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/fields/text_field.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter/services.dart';\nimport 'package:flutter_form_builder/flutter_form_builder.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../../../theme/color_manager.dart';\n\nclass BaseTextField extends StatefulWidget {\n  final FocusNode? focusNode;\n  final TextEditingController? controller;\n  final TextInputType textInputType;\n  final Function(dynamic)? onChanged;\n  final TextAlign textAlign;\n  final Function()? onTap;\n  final EdgeInsetsGeometry? contentPadding;\n  final Widget? icon;\n  final Widget? suffixIcon;\n  final String? label;\n  final String name;\n  final String? hint;\n  final int maxLines;\n  final String? ignoringMessage;\n  final String? Function(String?)? validator;\n  final bool isObscure;\n  final bool isRequired;\n  final bool withoutEnter;\n  final String? initialValue;\n  final String? title;\n  final bool? enabled;\n  final bool useUnderlineBorder;\n  final String? Function(String?)? realTimeValidator;\n\n  const BaseTextField({\n    super.key,\n    this.ignoringMessage,\n    required this.name,\n    this.enabled = true,\n    this.focusNode,\n    this.controller,\n    this.isObscure = false,\n    this.withoutEnter = false,\n    this.onTap,\n    this.hint,\n    this.icon,\n    this.suffixIcon,\n    this.label,\n    this.onChanged,\n    this.initialValue,\n    this.textAlign = TextAlign.start,\n    this.contentPadding,\n    this.textInputType = TextInputType.text,\n    this.maxLines = 1,\n    this.isRequired = true,\n    this.validator,\n    this.title,\n    this.useUnderlineBorder = false,\n    this.realTimeValidator,\n  });\n\n  @override\n  State<BaseTextField> createState() => _BaseTextFieldState();\n}\n\nclass _BaseTextFieldState extends State<BaseTextField> {\n  late bool _isObscure;\n  String? _realTimeError;\n\n  @override\n  void initState() {\n    super.initState();\n    _isObscure = widget.isObscure;\n  }\n\n  void _onTextChanged(String? value) {\n    if (widget.realTimeValidator != null) {\n      setState(() {\n        _realTimeError = widget.realTimeValidator!(value);\n      });\n    }\n    if (widget.onChanged != null) {\n      widget.onChanged!(value);\n    }\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    if (widget.title != null) {\n      return Column(\n        crossAxisAlignment: CrossAxisAlignment.start,\n        children: [\n          Text(\n            widget.title!,\n            style: AppTextStyles.labelLarge.copyWith(\n              fontWeight: FontWeight.bold,\n              fontSize: 16,\n              color: Colors.black,\n            ),\n          ),\n          AppGaps.gap8,\n          _buildTextField(context),\n          if (_realTimeError != null) ...[\n            AppGaps.gap4,\n            Text(\n              _realTimeError!,\n              style: AppTextStyles.labelSmall.copyWith(\n                color: Colors.red,\n              ),\n            ),\n          ],\n        ],\n      );\n    }\n    return _buildTextField(context);\n  }\n\n  Widget _buildTextField(BuildContext context) {\n    return FormBuilderTextField(\n      decoration: InputDecoration(\n        labelText: widget.label,\n        hintText: widget.withoutEnter\n            ? widget.hint\n            : widget.hint ?? widget.title ?? widget.label,\n        hintStyle: AppTextStyles.labelMedium.copyWith(\n          color: Colors.grey,\n          fontSize: 14,\n        ),\n        labelStyle: AppTextStyles.labelMedium,\n        prefixIcon: widget.icon != null\n            ? Padding(\n                padding: const EdgeInsets.all(8.0),\n                child: widget.icon,\n              )\n            : null,\n        suffixIcon: widget.textInputType == TextInputType.visiblePassword\n            ? InkWell(\n                onTap: () {\n                  setState(() {\n                    _isObscure = !_isObscure;\n                  });\n                },\n                child: Icon(\n                  _isObscure\n                      ? Icons.visibility_off_outlined\n                      : Icons.visibility_outlined,\n                  color: Colors.grey,\n                ),\n              )\n            : widget.suffixIcon != null\n                ? Padding(\n                    padding: const EdgeInsets.all(8.0),\n                    child: widget.suffixIcon,\n                  )\n                : null,\n        border: widget.useUnderlineBorder\n            ? const UnderlineInputBorder(\n                borderSide:\n                    BorderSide(color: ColorManager.primaryColor, width: 1.0),\n              )\n            : null,\n        enabledBorder: widget.useUnderlineBorder\n            ? const UnderlineInputBorder(\n                borderSide:\n                    BorderSide(color: ColorManager.primaryColor, width: 1.0),\n              )\n            : null,\n        focusedBorder: widget.useUnderlineBorder\n            ? const UnderlineInputBorder(\n                borderSide:\n                    BorderSide(color: ColorManager.primaryColor, width: 2.0),\n              )\n            : null,\n        contentPadding: widget.useUnderlineBorder\n            ? const EdgeInsets.only(top: 15, bottom: 10)\n            : const EdgeInsets.symmetric(\n                horizontal: AppSpaces.padding8,\n                vertical: AppSpaces.padding12 + 5,\n              ),\n      ),\n      name: widget.name,\n      enableSuggestions: true,\n      onTapOutside: (_) {\n        FocusScope.of(context).unfocus();\n      },\n      focusNode: widget.focusNode,\n      obscureText: widget.isObscure ? _isObscure : false,\n      controller: widget.controller,\n      keyboardType: widget.textInputType,\n      inputFormatters: [\n        if (widget.textInputType == TextInputType.number)\n          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]')),\n      ],\n      textAlign: widget.textAlign,\n      onChanged: _onTextChanged,\n      enabled: widget.enabled ?? true,\n      onTap: widget.onTap,\n      initialValue: widget.initialValue,\n      maxLines: widget.maxLines,\n      validator: widget.validator,\n      style: AppTextStyles.labelMedium.copyWith(\n        fontSize: 16,\n        color: Colors.black87,\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756976688158}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/register_header.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/register_header.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter/services.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass RegisterHeader extends StatelessWidget {\n  const RegisterHeader({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return AnnotatedRegion<SystemUiOverlayStyle>(\n      value: SystemUiOverlayStyle.light.copyWith(\n        statusBarColor: ColorManager.primaryColor,\n        systemNavigationBarColor: Colors.white,\n        systemNavigationBarIconBrightness: Brightness.dark,\n      ),\n      child: Stack(\n        children: [\n          // Background with curved design\n          Assets.images.topClipper.image(\n            width: double.infinity,\n            fit: BoxFit.cover,\n          ),\n          SafeArea(\n            child: Padding(\n              padding: const EdgeInsets.symmetric(\n                horizontal: 24.0,\n                vertical: 8.0,\n              ),\n              child: Row(\n                mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                crossAxisAlignment: CrossAxisAlignment.start,\n                children: [\n                  Padding(\n                    padding: const EdgeInsets.only(top: 10.0),\n                    child: Text(\n                      context.tr.createAccount,\n                      style: AppTextStyles.title.copyWith(\n                        color: ColorManager.white,\n                        fontSize: 32,\n                        fontWeight: FontWeight.bold,\n                      ),\n                    ),\n                  ),\n                  // Logo\n                  Assets.images.logoSymbol.image(\n                    width: 50,\n                    height: 50,\n                    color: ColorManager.white,\n                    errorBuilder: (context, error, stackTrace) {\n                      return const Icon(\n                        Icons.account_balance,\n                        color: ColorManager.white,\n                        size: 50,\n                      );\n                    },\n                  ),\n                ],\n              ),\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 50, "oldContent": "                    color: ColorManager.white,"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 35, "oldContent": "                  Padding("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                    padding: const EdgeInsets.only(top: 10.0),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                    child: Text("}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                      context.tr.createAccount,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                      style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                        color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                        fontSize: 32,"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                      ),"}, {"type": "INSERT", "lineNumber": 35, "content": "                  Text("}, {"type": "INSERT", "lineNumber": 36, "content": "                    context.tr.createAccount,"}, {"type": "INSERT", "lineNumber": 37, "content": "                    style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 38, "content": "                      color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 39, "content": "                      fontSize: 32,"}, {"type": "INSERT", "lineNumber": 40, "content": "                      fontWeight: FontWeight.bold,"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "                vertical: 12.0,", "oldContent": "                vertical: 8.0,"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/terms_checkbox.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/terms_checkbox.widget.dart", "baseContent": "import 'package:flutter/gestures.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:google_fonts/google_fonts.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/auth/view/register/widgets/terms_dialog.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass TermsCheckbox extends HookWidget {\n  final ValueNotifier<bool> termsAccepted;\n\n  const TermsCheckbox({\n    super.key,\n    required this.termsAccepted,\n  });\n\n  @override\n  Widget build(BuildContext context) {\n    return ListTile(\n      contentPadding: EdgeInsets.zero,\n      leading: SizedBox(\n        width: 24,\n        height: 24,\n        child: Checkbox(\n          value: termsAccepted.value,\n          \n          onChanged: (bool? value) {\n            termsAccepted.value = value ?? false;\n          },\n          activeColor: ColorManager.primaryColor,\n          side: const BorderSide(color: Colors.black, width: 1.5),\n          shape: RoundedRectangleBorder(\n            borderRadius: BorderRadius.circular(4),\n          ),\n        ),\n      ),\n      title: RichText(\n        text: TextSpan(\n          style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),\n          children: [\n            TextSpan(text: context.tr.termsAndConditionsText),\n            const TextSpan(text: ' '),\n            TextSpan(\n              text: context.tr.termsAndConditionsLink,\n              style: const TextStyle(\n                color: ColorManager.termsLinkColor,\n                decoration: TextDecoration.underline,\n              ),\n              recognizer: TapGestureRecognizer()\n                ..onTap = () {\n                  TermsDialog.show(context);\n                },\n            ),\n            const TextSpan(text: ' '),\n            TextSpan(text: context.tr.termsAndConditionsEnd),\n          ],\n        ),\n      ),\n      onTap: () {\n        termsAccepted.value = !termsAccepted.value;\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756978034437, "deltas": [{"timestamp": 1756978036587, "changes": [{"type": "MODIFY", "lineNumber": 26, "content": "          checkColor: ColorManager.white,", "oldContent": "          "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/register_buttons.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/register_buttons.widget.dart", "baseContent": "import 'dart:developer';\n\nimport 'package:flutter/material.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/auth/view/login/login.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass RegisterButtons extends StatelessWidget {\n  final bool isLoading;\n  final bool isFormValid;\n  final VoidCallback onRegister;\n\n  const RegisterButtons({\n    super.key,\n    required this.isLoading,\n    required this.isFormValid,\n    required this.onRegister,\n  });\n\n  @override\n  Widget build(BuildContext context) {\n    log('asfsafsaf $isFormValid');\n    return Column(\n      children: [\n        // Register Button\n        SizedBox(\n          height: 50,\n          width: double.infinity,\n          child: ElevatedButton(\n            onPressed: isFormValid && !isLoading ? onRegister : null,\n            style: ElevatedButton.styleFrom(\n              backgroundColor: isFormValid\n                  ? ColorManager.primaryColor\n                  : ColorManager.disabledButtonColor,\n              foregroundColor: isFormValid ? ColorManager.white : Colors.black,\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(12),\n              ),\n              elevation: 0,\n            ),\n            child: isLoading\n                ? const SizedBox(\n                    height: 20,\n                    width: 20,\n                    child: CircularProgressIndicator(\n                      strokeWidth: 2,\n                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),\n                    ),\n                  )\n                : Text(\n                    context.tr.createAccount,\n                    style: AppTextStyles.labelLarge.copyWith(\n                      fontSize: 18,\n                      fontWeight: FontWeight.bold,\n                    ),\n                  ),\n          ),\n        ),\n\n        AppGaps.gap16,\n\n        // Have Account Text\n        Text(\n          context.tr.haveAnAccount,\n          textAlign: TextAlign.center,\n          style: AppTextStyles.labelMedium.copyWith(\n            color: Colors.black54,\n            fontSize: 14,\n          ),\n        ),\n\n        AppGaps.gap8,\n\n        // Login Button\n        SizedBox(\n          height: 50,\n          width: double.infinity,\n          child: OutlinedButton(\n            onPressed: () {\n              const LoginScreen().navigate;\n            },\n            style: OutlinedButton.styleFrom(\n              foregroundColor: ColorManager.primaryColor,\n              side: const BorderSide(\n                color: ColorManager.primaryColor,\n                width: 1.5,\n              ),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(12),\n              ),\n            ),\n            child: Text(\n              context.tr.login,\n              style: AppTextStyles.labelLarge.copyWith(\n                fontSize: 18,\n                fontWeight: FontWeight.bold,\n                color: ColorManager.primaryColor,\n              ),\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756978264669, "deltas": [{"timestamp": 1756978270564, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "    log('asfsafsaf $isFormValid');"}, {"type": "MODIFY", "lineNumber": 31, "content": "              backgroundColor: !isFormValid", "oldContent": "              backgroundColor: isFormValid"}]}, {"timestamp": 1756978273034, "changes": [{"type": "MODIFY", "lineNumber": 30, "content": "            style: ElevatedButton.styleFrom(", "oldContent": "              backgroundColor: !isFormValid"}]}, {"timestamp": 1756978278098, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "            onPressed: null,", "oldContent": "            onPressed: isFormValid && !isLoading ? onRegister : null,"}, {"type": "INSERT", "lineNumber": 30, "content": "            // isFormValid && !isLoading ? onRegister : null,"}]}, {"timestamp": 1756978281009, "changes": [{"type": "DELETE", "lineNumber": 29, "oldContent": "            onPressed: null,"}, {"type": "MODIFY", "lineNumber": 29, "content": "            onPressed: isFormValid && !isLoading ? onRegister : null,", "oldContent": "            // isFormValid && !isLoading ? onRegister : null,"}]}, {"timestamp": 1756978285880, "changes": [{"type": "MODIFY", "lineNumber": 28, "content": "          child: <PERSON><PERSON>(", "oldContent": "          child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}]}, {"timestamp": 1756978290971, "changes": [{"type": "DELETE", "lineNumber": 30, "oldContent": "            style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 31, "oldContent": "              backgroundColor: isFormValid"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                  ? ColorManager.primaryColor"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                  : ColorManager.disabledButtonColor,"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "              foregroundColor: isFormValid ? ColorManager.white : Colors.black,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "              shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "              elevation: 0,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "            child: is<PERSON><PERSON>ding"}, {"type": "INSERT", "lineNumber": 30, "content": "           "}, {"type": "INSERT", "lineNumber": 31, "content": "            label: isLoading"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 41, "content": "                    ,", "oldContent": "                    context.tr.createAccount,"}]}, {"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 2, "content": "import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "           "}, {"type": "DELETE", "lineNumber": 31, "oldContent": "            label: isLoading"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                ? const SizedBox("}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                    height: 20,"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                    width: 20,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                    child: CircularProgressIndicator("}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                      strokeWidth: 2,"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                : Text("}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                    ,"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    style: AppTextStyles.labelLarge.copyWith("}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                      fontSize: 18,"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                  ),"}, {"type": "INSERT", "lineNumber": 31, "content": "            isLoading: isLoading,"}, {"type": "INSERT", "lineNumber": 32, "content": "            loadingWidget:LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 33, "content": "            "}, {"type": "INSERT", "lineNumber": 34, "content": "            label: context.tr.createAccount,"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 33, "content": "            color: ColorManager.primaryColor,", "oldContent": "            "}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 32, "oldContent": "            loadingWidget:LoadingWidget(),"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "            color: ColorManager.primaryColor,"}, {"type": "INSERT", "lineNumber": 32, "content": "            loadingWidget: LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 33, "content": "            color: isFormValid ? ColorManager.primaryColor : Colors.grey,"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 33, "content": "            color: isFormValid", "oldContent": "            color: isFormValid ? ColorManager.primaryColor : Colors.grey,"}, {"type": "INSERT", "lineNumber": 34, "content": "                ? ColorManager.primaryColor"}, {"type": "INSERT", "lineNumber": 35, "content": "                : ColorManager.disabledButtonColor,"}]}, {"timestamp": 1757273248981, "changes": [{"type": "DELETE", "lineNumber": 33, "oldContent": "            color: isFormValid"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                ? ColorManager.primaryColor"}, {"type": "INSERT", "lineNumber": 33, "content": ""}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                : ColorManager.disabledButtonColor,"}]}, {"timestamp": 1757273251171, "changes": [{"type": "DELETE", "lineNumber": 33, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/models/user_model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/models/user_model.dart", "baseContent": "import 'package:equatable/equatable.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\n\nclass UserModel extends Equatable {\n  final int? id;\n  final String? name;\n  final String? email;\n  final String? mobile;\n  final String? password;\n  final String? deviceToken;\n\n  const UserModel({\n    this.id,\n    this.name,\n    this.email,\n    this.mobile,\n    this.password,\n    this.deviceToken,\n  });\n\n  // * For Login ================================\n  factory UserModel.fromJson(Map<String, dynamic> json) {\n    return UserModel(\n      id: json['id'],\n      name: json['name'] ?? '',\n      email: json['email'] ?? '',\n      mobile: json['phone'] ?? '',\n    );\n  }\n\n  //? Copy With\n\n  // * To Json ================================\n  Map<String, dynamic> toJson() {\n    return {\n      'name': name,\n      'email_phone': mobile,\n      'fcm_token': deviceToken,\n      'password': password,\n      'password_confirmation': password,\n    };\n  }\n\n  // * To Login Json ================================\n  Map<String, dynamic> toLoginJson() {\n    return {\n      'name': name,\n      'email_phone': mobile,\n      'fcm_token': deviceToken,\n      'password': password,\n    };\n  }\n\n  @override\n  List<Object?> get props => [\n        id,\n        name,\n        email,\n        mobile,\n        password,\n        deviceToken,\n      ];\n\n  //? Get saved user\n  static UserModel currentUser() {\n    final userData = GetStorageService.getData(key: LocalKeys.user);\n\n    if (userData == null) {\n      return const UserModel();\n    } else {\n      return UserModel.fromJson(userData);\n    }\n  }\n}\n", "baseTimestamp": 1757268737665}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/controllers/auth_controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/controllers/auth_controller.dart", "baseContent": "import 'package:dropx/src/core/consts/network/api_strings.dart';\nimport 'package:dropx/src/screens/auth/models/user_model.dart';\nimport 'package:dropx/src/screens/auth/repositories/auth_repository.dart';\nimport 'package:dropx/src/screens/auth/view/login/login.screen.dart';\nimport 'package:dropx/src/screens/main_screen/view/main.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AuthController extends BaseVM {\n  final AuthRepository authRepo;\n\n  AuthController({\n    required this.authRepo,\n  });\n\n  // * Login\n  Future<bool> login({\n    required Map<String, dynamic> data,\n  }) async {\n    return await baseFunction(\n      () async {\n        final user = await _setUser(data);\n\n        final userData = await authRepo.login(user: user);\n\n        return userData;\n      },\n      additionalFunction: () {\n        const MainScreen().navigate;\n      },\n    );\n  }\n\n  // * Register\n  Future<bool> register({\n    required Map<String, dynamic> data,\n  }) async {\n    return await baseFunction(\n      () async {\n        final user = await _setUser(data);\n\n        final userData = await authRepo.register(user: user);\n\n        return userData;\n      },\n      additionalFunction: () {\n        const MainScreen().navigate;\n      },\n    );\n  }\n\n  // * Add Social Media\n  Future<void> addSocialMedia({\n    required List<SocialModel> social,\n  }) async {\n    return await baseFunction(\n      () async {\n        await authRepo.addSocialMedia(social: social);\n      },\n    );\n  }\n\n  // * Set User\n  Future<UserModel> _setUser(\n    Map<String, dynamic> data,\n  ) async {\n    // final fcmToken = await NotificationService.getToken();\n\n    final user = UserModel(\n      name: data[FieldsConsts.name],\n      mobile: data[FieldsConsts.mobile],\n      password: data[FieldsConsts.password],\n      deviceToken: 'test',\n    );\n\n    return user;\n  }\n\n  // * Logout\n  Future<void> logout() async {\n    return await baseFunction(\n      () async {\n        await authRepo.logout();\n\n        const LoginScreen().navigateReplacement;\n      },\n    );\n  }\n\n// * Get Countries\n// Future<List<CountryModel>> getCountries() async {\n//   return await baseFunction(\n//     () async {\n//       return await authRepo.getCountries();\n//     },\n//   );\n// }\n}\n", "baseTimestamp": 1757268762537, "deltas": [{"timestamp": 1757268768026, "changes": [{"type": "DELETE", "lineNumber": 50, "oldContent": "  // * Add Social Media"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  Future<void> addSocialMedia({"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "    required List<SocialModel> social,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  }) async {"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "    return await baseFunction("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "      () async {"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "        await authRepo.addSocialMedia(social: social);"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  }"}]}, {"timestamp": 1757271447358, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "import 'package:dropx/src/core/shared/extensions/context_extensions.dart';"}, {"type": "INSERT", "lineNumber": 5, "content": "import 'package:dropx/src/screens/auth/view/verification/verification.screen.dart';"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "        final user = await _setUser(data);"}, {"type": "INSERT", "lineNumber": 22, "content": "        final user = await _setLoginUser(data);"}, {"type": "INSERT", "lineNumber": 29, "content": "        showToast(context.tr.loginSuccessful);"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  Future<bool> register({"}, {"type": "INSERT", "lineNumber": 36, "content": "  Future<Map<String, dynamic>> register({"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        final user = await _setUser(data);"}, {"type": "INSERT", "lineNumber": 41, "content": "        final user = await _setRegisterUser(data);"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "        final userData = await authRepo.register(user: user);"}, {"type": "INSERT", "lineNumber": 43, "content": "        final response = await authRepo.register(user: user);"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "        return userData;"}, {"type": "INSERT", "lineNumber": 45, "content": "        return response;"}, {"type": "INSERT", "lineNumber": 47, "content": "      additionalFunction: (response) {"}, {"type": "INSERT", "lineNumber": 48, "content": "        showToast(navService.context.tr.verificationCodeSent);"}, {"type": "INSERT", "lineNumber": 49, "content": "        VerificationScreen("}, {"type": "INSERT", "lineNumber": 50, "content": "          phone: data[FieldsConsts.mobile],"}, {"type": "INSERT", "lineNumber": 51, "content": "          userData: data,"}, {"type": "INSERT", "lineNumber": 52, "content": "        ).navigate;"}, {"type": "INSERT", "lineNumber": 53, "content": "      },"}, {"type": "INSERT", "lineNumber": 54, "content": "    );"}, {"type": "INSERT", "lineNumber": 55, "content": "  }"}, {"type": "INSERT", "lineNumber": 56, "content": ""}, {"type": "INSERT", "lineNumber": 57, "content": "  // * Verify Code"}, {"type": "INSERT", "lineNumber": 58, "content": "  Future<bool> verifyCode({"}, {"type": "INSERT", "lineNumber": 59, "content": "    required Map<String, dynamic> userData,"}, {"type": "INSERT", "lineNumber": 60, "content": "    required String code,"}, {"type": "INSERT", "lineNumber": 61, "content": "  }) async {"}, {"type": "INSERT", "lineNumber": 62, "content": "    return await baseFunction("}, {"type": "INSERT", "lineNumber": 63, "content": "      () async {"}, {"type": "INSERT", "lineNumber": 64, "content": "        final user = await _setRegisterUser(userData);"}, {"type": "INSERT", "lineNumber": 65, "content": ""}, {"type": "INSERT", "lineNumber": 66, "content": "        final result = await authRepo.verifyCode(user: user, code: code);"}, {"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 68, "content": "        return result;"}, {"type": "INSERT", "lineNumber": 69, "content": "      },"}, {"type": "INSERT", "lineNumber": 71, "content": "        showToast(navService.context.tr.registrationSuccessful);"}, {"type": "DELETE", "lineNumber": 50, "oldContent": ""}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  // * Set User"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  Future<UserModel> _setUser("}, {"type": "INSERT", "lineNumber": 77, "content": "  // * Set Login User"}, {"type": "INSERT", "lineNumber": 78, "content": "  Future<UserModel> _setLoginUser("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "    // final fcmToken = await NotificationService.getToken();"}, {"type": "INSERT", "lineNumber": 81, "content": "    final user = UserModel("}, {"type": "INSERT", "lineNumber": 82, "content": "      phone: data[FieldsConsts.mobile],"}, {"type": "INSERT", "lineNumber": 83, "content": "      password: data[FieldsConsts.password],"}, {"type": "INSERT", "lineNumber": 84, "content": "    );"}, {"type": "INSERT", "lineNumber": 86, "content": "    return user;"}, {"type": "INSERT", "lineNumber": 87, "content": "  }"}, {"type": "INSERT", "lineNumber": 88, "content": ""}, {"type": "INSERT", "lineNumber": 89, "content": "  // * Set Register User"}, {"type": "INSERT", "lineNumber": 90, "content": "  Future<UserModel> _setRegisterUser("}, {"type": "INSERT", "lineNumber": 91, "content": "    Map<String, dynamic> data,"}, {"type": "INSERT", "lineNumber": 92, "content": "  ) async {"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "      mobile: data[FieldsConsts.mobile],"}, {"type": "INSERT", "lineNumber": 95, "content": "      phone: data[FieldsConsts.mobile],"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "      deviceToken: 'test',"}, {"type": "INSERT", "lineNumber": 97, "content": "      nationalId: data[FieldsConsts.idNumber],"}]}, {"timestamp": 1757271462350, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import 'package:dropx/src/screens/main_screen/view/main.screen.dart';", "oldContent": "import 'package:dropx/src/screens/main_screen/view/main.screen.dart';"}, {"type": "MODIFY", "lineNumber": 23, "content": "", "oldContent": ""}, {"type": "DELETE", "lineNumber": 31, "oldContent": "        showToast(context.tr.loginSuccessful);"}, {"type": "INSERT", "lineNumber": 35, "content": "  Future<Map<String, dynamic>> register({"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  Future<Map<String, dynamic>> register({"}, {"type": "DELETE", "lineNumber": 41, "oldContent": ""}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "      additionalFunction: () {"}, {"type": "INSERT", "lineNumber": 43, "content": ""}, {"type": "DELETE", "lineNumber": 48, "oldContent": "        const MainScreen().navigate;"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 55, "oldContent": ""}, {"type": "DELETE", "lineNumber": 60, "oldContent": "    Map<String, dynamic> data,"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  ) async {"}, {"type": "DELETE", "lineNumber": 65, "oldContent": ""}, {"type": "DELETE", "lineNumber": 67, "oldContent": "    final user = UserModel("}, {"type": "DELETE", "lineNumber": 69, "oldContent": "      name: data[FieldsConsts.name],"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "      password: data[FieldsConsts.password],"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 77, "oldContent": ""}, {"type": "DELETE", "lineNumber": 79, "oldContent": "    return user;"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 67, "content": "        return result;"}, {"type": "INSERT", "lineNumber": 68, "content": "      },"}, {"type": "INSERT", "lineNumber": 69, "content": "      additionalFunction: () {"}, {"type": "INSERT", "lineNumber": 70, "content": "        showToast(navService.context.tr.registrationSuccessful);"}, {"type": "INSERT", "lineNumber": 71, "content": "        const MainScreen().navigate;"}, {"type": "INSERT", "lineNumber": 72, "content": "      },"}, {"type": "INSERT", "lineNumber": 73, "content": "    );"}, {"type": "INSERT", "lineNumber": 74, "content": "  }"}, {"type": "INSERT", "lineNumber": 76, "content": "  // * Set Login User"}, {"type": "INSERT", "lineNumber": 77, "content": "  Future<UserModel> _setLoginUser("}, {"type": "INSERT", "lineNumber": 78, "content": "    Map<String, dynamic> data,"}, {"type": "INSERT", "lineNumber": 79, "content": "  ) async {"}, {"type": "INSERT", "lineNumber": 80, "content": "    final user = UserModel("}, {"type": "INSERT", "lineNumber": 81, "content": "      phone: data[FieldsConsts.mobile],"}, {"type": "INSERT", "lineNumber": 82, "content": "      password: data[FieldsConsts.password],"}, {"type": "INSERT", "lineNumber": 83, "content": "    );"}, {"type": "INSERT", "lineNumber": 84, "content": ""}, {"type": "INSERT", "lineNumber": 85, "content": "    return user;"}, {"type": "INSERT", "lineNumber": 86, "content": "  }"}, {"type": "INSERT", "lineNumber": 87, "content": ""}, {"type": "INSERT", "lineNumber": 88, "content": "  // * Set Register User"}, {"type": "INSERT", "lineNumber": 89, "content": "  Future<UserModel> _setRegisterUser("}, {"type": "INSERT", "lineNumber": 90, "content": "    Map<String, dynamic> data,"}, {"type": "INSERT", "lineNumber": 91, "content": "  ) async {"}, {"type": "INSERT", "lineNumber": 92, "content": "    final user = UserModel("}, {"type": "INSERT", "lineNumber": 93, "content": "      name: data[FieldsConsts.name],"}, {"type": "INSERT", "lineNumber": 94, "content": "      phone: data[FieldsConsts.mobile],"}, {"type": "INSERT", "lineNumber": 95, "content": "      password: data[FieldsConsts.password],"}, {"type": "INSERT", "lineNumber": 96, "content": "      nationalId: data[FieldsConsts.idNumber],"}, {"type": "INSERT", "lineNumber": 97, "content": "    );"}, {"type": "INSERT", "lineNumber": 98, "content": ""}, {"type": "INSERT", "lineNumber": 99, "content": "    return user;"}, {"type": "INSERT", "lineNumber": 100, "content": "  }"}, {"type": "INSERT", "lineNumber": 101, "content": ""}, {"type": "DELETE", "lineNumber": 86, "oldContent": "        return result;"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "        showToast(navService.context.tr.registrationSuccessful);"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "  // * Set Login User"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "  Future<UserModel> _setLoginUser("}, {"type": "DELETE", "lineNumber": 104, "oldContent": "    final user = UserModel("}, {"type": "DELETE", "lineNumber": 106, "oldContent": "      phone: data[FieldsConsts.mobile],"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "      password: data[FieldsConsts.password],"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "    return user;"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 117, "oldContent": ""}, {"type": "DELETE", "lineNumber": 118, "oldContent": "      nationalId: data[FieldsConsts.idNumber],"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "  // * Set Register User"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "      phone: data[FieldsConsts.mobile],"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "  Future<UserModel> _setRegisterUser("}, {"type": "DELETE", "lineNumber": 122, "oldContent": "  ) async {"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "    Map<String, dynamic> data,"}]}, {"timestamp": 1757271468740, "changes": [{"type": "INSERT", "lineNumber": 5, "content": "import 'package:dropx/src/screens/auth/view/verification/verification.screen.dart';"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import 'package:dropx/src/screens/main_screen/view/main.screen.dart';"}, {"type": "INSERT", "lineNumber": 22, "content": "        final user = await _setLoginUser(data);"}, {"type": "DELETE", "lineNumber": 23, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 35, "content": "  Future<Map<String, dynamic>> register({", "oldContent": "  Future<Map<String, dynamic>> register({"}, {"type": "MODIFY", "lineNumber": 43, "content": "", "oldContent": ""}, {"type": "DELETE", "lineNumber": 46, "oldContent": "      additionalFunction: (response) {"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        showToast(navService.context.tr.verificationCodeSent);"}, {"type": "INSERT", "lineNumber": 46, "content": "      additionalFunction: () {"}, {"type": "INSERT", "lineNumber": 56, "content": "  Future<bool> verifyCode({"}, {"type": "INSERT", "lineNumber": 57, "content": "    required Map<String, dynamic> userData,"}, {"type": "INSERT", "lineNumber": 58, "content": "    required String code,"}, {"type": "INSERT", "lineNumber": 59, "content": "  }) async {"}, {"type": "INSERT", "lineNumber": 60, "content": "    return await baseFunction("}, {"type": "INSERT", "lineNumber": 61, "content": "      () async {"}, {"type": "INSERT", "lineNumber": 62, "content": "        final user = await _setRegisterUser(userData);"}, {"type": "INSERT", "lineNumber": 63, "content": ""}, {"type": "INSERT", "lineNumber": 64, "content": "        final result = await authRepo.verifyCode(user: user, code: code);"}, {"type": "INSERT", "lineNumber": 65, "content": ""}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  Future<bool> verifyCode({"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "    required Map<String, dynamic> userData,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "    required String code,"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "  }) async {"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "    return await baseFunction("}, {"type": "INSERT", "lineNumber": 74, "content": ""}, {"type": "DELETE", "lineNumber": 71, "oldContent": "      () async {"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "        final user = await _setRegisterUser(userData);"}, {"type": "DELETE", "lineNumber": 77, "oldContent": ""}, {"type": "DELETE", "lineNumber": 80, "oldContent": "        final result = await authRepo.verifyCode(user: user, code: code);"}, {"type": "DELETE", "lineNumber": 83, "oldContent": ""}, {"type": "DELETE", "lineNumber": 84, "oldContent": ""}, {"type": "DELETE", "lineNumber": 86, "oldContent": "  // * Logout"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "  Future<void> logout() async {"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "    return await baseFunction("}, {"type": "DELETE", "lineNumber": 94, "oldContent": "      () async {"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "        await authRepo.logout();"}, {"type": "DELETE", "lineNumber": 99, "oldContent": ""}, {"type": "DELETE", "lineNumber": 101, "oldContent": "        const LoginScreen().navigateReplacement;"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 99, "content": "  }"}, {"type": "INSERT", "lineNumber": 101, "content": "  // * Logout"}, {"type": "INSERT", "lineNumber": 102, "content": "  Future<void> logout() async {"}, {"type": "INSERT", "lineNumber": 103, "content": "    return await baseFunction("}, {"type": "INSERT", "lineNumber": 104, "content": "      () async {"}, {"type": "INSERT", "lineNumber": 105, "content": "        await authRepo.logout();"}, {"type": "INSERT", "lineNumber": 106, "content": ""}, {"type": "INSERT", "lineNumber": 107, "content": "        const LoginScreen().navigateReplacement;"}, {"type": "INSERT", "lineNumber": 108, "content": "      },"}, {"type": "INSERT", "lineNumber": 109, "content": "    );"}]}, {"timestamp": 1757271475445, "changes": [{"type": "INSERT", "lineNumber": 34, "content": "  // * Register"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  Future<Map<String, dynamic>> register({"}, {"type": "INSERT", "lineNumber": 42, "content": "        final response = await authRepo.register(user: user);"}, {"type": "DELETE", "lineNumber": 43, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 56, "content": "  Future<bool> verifyCode({", "oldContent": "  Future<bool> verifyCode({"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "        return result;"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "      additionalFunction: () {"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "        showToast(navService.context.tr.registrationSuccessful);"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        const MainScreen().navigate;"}, {"type": "INSERT", "lineNumber": 66, "content": "        return result;"}, {"type": "INSERT", "lineNumber": 68, "content": "      additionalFunction: () {"}, {"type": "INSERT", "lineNumber": 69, "content": "        const MainScreen().navigate;"}, {"type": "INSERT", "lineNumber": 70, "content": "      },"}, {"type": "INSERT", "lineNumber": 73, "content": ""}, {"type": "DELETE", "lineNumber": 77, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 93, "content": "      password: data[FieldsConsts.password],", "oldContent": "  }"}, {"type": "INSERT", "lineNumber": 94, "content": "      nationalId: data[FieldsConsts.idNumber],"}, {"type": "INSERT", "lineNumber": 95, "content": "    );"}, {"type": "INSERT", "lineNumber": 96, "content": ""}, {"type": "INSERT", "lineNumber": 97, "content": "    return user;"}, {"type": "INSERT", "lineNumber": 98, "content": "  }"}, {"type": "INSERT", "lineNumber": 99, "content": ""}, {"type": "DELETE", "lineNumber": 97, "oldContent": "      password: data[FieldsConsts.password],"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "      nationalId: data[FieldsConsts.idNumber],"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 106, "oldContent": ""}, {"type": "DELETE", "lineNumber": 108, "oldContent": "    return user;"}, {"type": "DELETE", "lineNumber": 109, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/repositories/auth_repository.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/repositories/auth_repository.dart", "baseContent": "import 'package:dropx/src/core/consts/network/api_endpoints.dart';\nimport 'package:dropx/src/screens/auth/models/user_model.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AuthRepository with BaseRepository {\n  final BaseApiServices networkApiService;\n\n  AuthRepository({\n    required this.networkApiService,\n  });\n\n  // * Login\n  Future<bool> login({\n    required UserModel user,\n  }) async {\n    return baseFunction(\n      () async {\n        const url = ApiEndpoints.login;\n\n        final response =\n            await networkApiService.postResponse(url, body: user.toLoginJson());\n\n        saveUserData(response);\n\n        return true;\n      },\n    );\n  }\n\n  // * Register\n  Future<bool> register({\n    required UserModel user,\n  }) async {\n    return baseFunction(\n      () async {\n        const url = ApiEndpoints.registerMobile;\n\n        final response =\n            await networkApiService.postResponse(url, body: user.toJson());\n\n        saveUserData(response);\n\n        return true;\n      },\n    );\n  }\n\n  // * Add Social Media\n  Future<void> addSocialMedia({\n    required List<SocialModel> social,\n  }) async {\n    return baseFunction(\n      () async {\n        const url = ApiEndpoints.addSocialMedia;\n\n        for (final socialData in social) {\n          await networkApiService.postResponse(url, body: socialData.toJson());\n        }\n      },\n    );\n  }\n\n  // * Save to local\n  void saveUserData(Map<String, dynamic> data) {\n    final userData = UserModel.fromJson(data['data']['user']);\n\n    GetStorageService.setData(\n      key: LocalKeys.user,\n      value: userData.toJson(),\n    );\n\n    GetStorageService.setData(\n      key: LocalKeys.token,\n      value: data['data']['token'],\n    );\n  }\n\n  // * Get Countries\n  // Future<List<CountryModel>> getCountries() async {\n  //   return baseFunction(\n  //     () async {\n  //       const url = ApiEndpoints.countries;\n  //\n  //       final response = await networkApiService.getResponse(url);\n  //\n  //       final countries = response['countries'] as List;\n  //\n  //       final countriesList =\n  //           countries.map((country) => CountryModel.fromJson(country)).toList();\n  //\n  //       return countriesList;\n  //     },\n  //   );\n  // }\n\n  // * Logout\n  Future<void> logout() async {\n    await baseFunction(\n      () async {\n        //! Clear Local Data\n        GetStorageService.clearLocalData();\n      },\n    );\n  }\n}\n", "baseTimestamp": 1757268772430, "deltas": [{"timestamp": 1757268781079, "changes": [{"type": "DELETE", "lineNumber": 46, "oldContent": ""}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  // * Add Social Media"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  Future<void> addSocialMedia({"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "    required List<SocialModel> social,"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  }) async {"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "    return baseFunction("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "      () async {"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        const url = ApiEndpoints.addSocialMedia;"}, {"type": "DELETE", "lineNumber": 54, "oldContent": ""}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        for (final socialData in social) {"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "          await networkApiService.postResponse(url, body: socialData.toJson());"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 61, "oldContent": ""}, {"type": "INSERT", "lineNumber": 46, "content": "  "}]}, {"timestamp": 1757268851648, "changes": [{"type": "MODIFY", "lineNumber": 46, "content": "", "oldContent": "  "}]}, {"timestamp": 1757269023857, "changes": [{"type": "MODIFY", "lineNumber": 30, "content": "  Future<Map<String, dynamic>> register({", "oldContent": "  Future<bool> register({"}, {"type": "MODIFY", "lineNumber": 35, "content": "        const url = ApiEndpoints.register;", "oldContent": "        const url = ApiEndpoints.registerMobile;"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        final response ="}, {"type": "MODIFY", "lineNumber": 37, "content": "        final response = await networkApiService.postResponse(url,", "oldContent": "            await networkApiService.postResponse(url, body: user.toJson());"}, {"type": "INSERT", "lineNumber": 38, "content": "            body: user.to<PERSON><PERSON><PERSON><PERSON><PERSON>());"}, {"type": "INSERT", "lineNumber": 40, "content": "        return response['data'];"}, {"type": "INSERT", "lineNumber": 41, "content": "      },"}, {"type": "INSERT", "lineNumber": 42, "content": "    );"}, {"type": "INSERT", "lineNumber": 43, "content": "  }"}, {"type": "INSERT", "lineNumber": 44, "content": ""}, {"type": "INSERT", "lineNumber": 45, "content": "  // * Verify Code"}, {"type": "INSERT", "lineNumber": 46, "content": "  Future<bool> verifyCode({"}, {"type": "INSERT", "lineNumber": 47, "content": "    required UserModel user,"}, {"type": "INSERT", "lineNumber": 48, "content": "    required String code,"}, {"type": "INSERT", "lineNumber": 49, "content": "  }) async {"}, {"type": "INSERT", "lineNumber": 50, "content": "    return baseFunction("}, {"type": "INSERT", "lineNumber": 51, "content": "      () async {"}, {"type": "INSERT", "lineNumber": 52, "content": "        const url = ApiEndpoints.verifyCode;"}, {"type": "INSERT", "lineNumber": 53, "content": ""}, {"type": "INSERT", "lineNumber": 54, "content": "        final response = await networkApiService.postResponse(url,"}, {"type": "INSERT", "lineNumber": 55, "content": "            body: user.to<PERSON>erify<PERSON><PERSON>(code));"}, {"type": "INSERT", "lineNumber": 56, "content": ""}]}, {"timestamp": 1757269033633, "changes": [{"type": "MODIFY", "lineNumber": 38, "content": "            body: user.to<PERSON><PERSON><PERSON><PERSON><PERSON>());", "oldContent": "            await networkApiService.postResponse(url, body: user.toJson());"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "        saveUserData(response);"}, {"type": "DELETE", "lineNumber": 43, "oldContent": ""}, {"type": "DELETE", "lineNumber": 45, "oldContent": "        return true;"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "      },"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 53, "oldContent": ""}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  // * Save to local"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  void saveUserData(Map<String, dynamic> data) {"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "    final userData = UserModel.fromJson(data['data']['user']);"}, {"type": "DELETE", "lineNumber": 61, "oldContent": ""}, {"type": "DELETE", "lineNumber": 63, "oldContent": "    GetStorageService.setData("}, {"type": "DELETE", "lineNumber": 65, "oldContent": "      key: LocalKeys.user,"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "      value: userData.toJson(),"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "    );"}, {"type": "INSERT", "lineNumber": 57, "content": "        saveUserData(response);"}, {"type": "INSERT", "lineNumber": 59, "content": "        return true;"}, {"type": "INSERT", "lineNumber": 60, "content": "      },"}, {"type": "INSERT", "lineNumber": 61, "content": "    );"}, {"type": "INSERT", "lineNumber": 62, "content": "  }"}, {"type": "INSERT", "lineNumber": 63, "content": ""}, {"type": "INSERT", "lineNumber": 64, "content": "  // * Save to local"}, {"type": "INSERT", "lineNumber": 65, "content": "  void saveUserData(Map<String, dynamic> data) {"}, {"type": "INSERT", "lineNumber": 66, "content": "    final userData = UserModel.fromJson(data['data']['user']);"}, {"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 69, "content": "      key: LocalKeys.user,"}, {"type": "INSERT", "lineNumber": 70, "content": "      value: userData.toRegisterJson(),"}, {"type": "INSERT", "lineNumber": 71, "content": "    );"}, {"type": "INSERT", "lineNumber": 72, "content": ""}, {"type": "INSERT", "lineNumber": 73, "content": "    GetStorageService.setData("}]}, {"timestamp": 1757269345216, "changes": [{"type": "DELETE", "lineNumber": 49, "oldContent": "        saveUserData(response);"}, {"type": "INSERT", "lineNumber": 50, "content": "    return baseFunction("}, {"type": "INSERT", "lineNumber": 51, "content": "      () async {"}, {"type": "INSERT", "lineNumber": 52, "content": "        const url = ApiEndpoints.verifyCode;"}, {"type": "INSERT", "lineNumber": 53, "content": ""}, {"type": "INSERT", "lineNumber": 54, "content": "        final response = await networkApiService.postResponse(url,"}, {"type": "INSERT", "lineNumber": 55, "content": "            body: user.to<PERSON>erify<PERSON><PERSON>(code));"}, {"type": "INSERT", "lineNumber": 56, "content": ""}, {"type": "INSERT", "lineNumber": 57, "content": "        saveUserData(response);"}, {"type": "INSERT", "lineNumber": 58, "content": ""}, {"type": "DELETE", "lineNumber": 53, "oldContent": "    return baseFunction("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "      () async {"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "        const url = ApiEndpoints.verifyCode;"}, {"type": "DELETE", "lineNumber": 63, "oldContent": ""}, {"type": "DELETE", "lineNumber": 64, "oldContent": "        final response = await networkApiService.postResponse(url,"}, {"type": "INSERT", "lineNumber": 68, "content": "    GetStorageService.setData("}, {"type": "DELETE", "lineNumber": 66, "oldContent": "      value: userData.toRegisterJson(),"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "            body: user.to<PERSON>erify<PERSON><PERSON>(code));"}, {"type": "INSERT", "lineNumber": 70, "content": "      value: userData.toJson(),"}, {"type": "DELETE", "lineNumber": 70, "oldContent": ""}, {"type": "DELETE", "lineNumber": 71, "oldContent": ""}, {"type": "DELETE", "lineNumber": 73, "oldContent": "    GetStorageService.setData("}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/doctor_fields.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/widgets/doctor_fields.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:google_maps_flutter/google_maps_flutter.dart';\nimport 'package:dropx/src/core/consts/network/api_strings.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/fields/base_image_picker.dart';\nimport 'package:dropx/src/core/shared/widgets/fields/text_field.dart';\nimport 'package:dropx/src/core/shared/widgets/map/map_picker.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass DoctorFieldsWidget extends ConsumerWidget {\n  final Map<String, ValueNotifier> valueNotifiers;\n\n  const DoctorFieldsWidget({\n    super.key,\n    required this.valueNotifiers,\n  });\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    return Column(\n      mainAxisSize: MainAxisSize.min,\n      children: [\n        Row(\n          children: [\n            Expanded(\n              child: BaseImagePicker(\n                name: FieldsConsts.doctor<PERSON>ogo,\n                label: context.tr.doctor<PERSON>ogo,\n              ),\n            ),\n            AppGaps.gap12,\n            Expanded(\n              child: BaseImagePicker(\n                name: FieldsConsts.doctorBackground,\n                label: context.tr.doctorBackground,\n              ),\n            ),\n          ],\n        ),\n\n        AppGaps.gap12,\n\n        //! Doctor Name ------------------------------\n        BaseTextField(\n          name: FieldsConsts.doctorName,\n          title: context.tr.doctorName,\n        ),\n\n        AppGaps.gap12,\n\n        //! Description ------------------------------\n        BaseTextField(\n          name: FieldsConsts.doctorDescription,\n          title: context.tr.description,\n          maxLines: 3,\n        ),\n\n        AppGaps.gap12,\n\n        //! Address ------------------------------\n        BaseTextField(\n          name: FieldsConsts.doctorAddress,\n          title: context.tr.address,\n          maxLines: 3,\n        ),\n\n        AppGaps.gap16,\n\n        SocialWidgetFields(\n            socialFields: valueNotifiers[FieldsConsts.social]!\n                as ValueNotifier<List<SocialModel>>),\n\n        AppGaps.gap16,\n\n        MapLocationPickerField(\n          selectedLocation:\n              valueNotifiers[FieldsConsts.doctorLoc]! as ValueNotifier<LatLng?>,\n        ),\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1757268785776}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/app_bar/base_header.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/app_bar/base_header.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter/services.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass BaseHeaderWidget extends StatelessWidget {\n  const BaseHeaderWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return AnnotatedRegion<SystemUiOverlayStyle>(\n      value: SystemUiOverlayStyle.light.copyWith(\n        statusBarColor: ColorManager.primaryColor,\n        systemNavigationBarColor: Colors.white,\n        systemNavigationBarIconBrightness: Brightness.dark,\n      ),\n      child: Stack(\n        children: [\n          // Background with curved design\n          Assets.images.topClipper.image(\n            width: double.infinity,\n            fit: BoxFit.cover,\n          ),\n          SafeArea(\n            child: Padding(\n              padding: const EdgeInsets.symmetric(\n                horizontal: 24.0,\n                vertical: 12.0,\n              ),\n              child: Row(\n                mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                crossAxisAlignment: CrossAxisAlignment.start,\n                children: [\n                  Text(\n                    context.tr.createAccount,\n                    style: AppTextStyles.title.copyWith(\n                      color: ColorManager.white,\n                      fontSize: 32,\n                      fontWeight: FontWeight.bold,\n                    ),\n                  ),\n                  // Logo\n                  Assets.images.logoSymbol.image(\n                    width: 50,\n                    height: 50,\n                    errorBuilder: (context, error, stackTrace) {\n                      return const Icon(\n                        Icons.account_balance,\n                        color: ColorManager.white,\n                        size: 50,\n                      );\n                    },\n                  ),\n                ],\n              ),\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 8, "content": "  final String title;"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "  const BaseHeaderWidget({", "oldContent": "  const BaseHeaderWidget({super.key});"}, {"type": "INSERT", "lineNumber": 11, "content": "    super.key,"}, {"type": "INSERT", "lineNumber": 12, "content": "    required this.title,"}, {"type": "INSERT", "lineNumber": 13, "content": "  });"}, {"type": "INSERT", "lineNumber": 14, "content": ""}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "", "oldContent": "  const BaseHeaderWidget({super.key});"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "INSERT", "lineNumber": 15, "content": "  @override"}, {"type": "INSERT", "lineNumber": 16, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 17, "content": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "MODIFY", "lineNumber": 41, "content": "                    title,", "oldContent": "                    context.tr.createAccount,"}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 8, "content": "  final String? title;", "oldContent": "  final String title;"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "    required this.title,"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 12, "content": "     this.title,"}, {"type": "INSERT", "lineNumber": 14, "content": ""}, {"type": "INSERT", "lineNumber": 15, "content": "  @override"}, {"type": "DELETE", "lineNumber": 17, "oldContent": ""}]}, {"timestamp": *************, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "", "oldContent": ""}, {"type": "INSERT", "lineNumber": 40, "content": "                  if(title != null)"}, {"type": "MODIFY", "lineNumber": 42, "content": "                    title!,", "oldContent": "                    title,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                  ),"}, {"type": "INSERT", "lineNumber": 48, "content": "                  )else "}, {"type": "INSERT", "lineNumber": 49, "content": "                    "}]}, {"timestamp": 1757273330809, "changes": [{"type": "INSERT", "lineNumber": 13, "content": "  });"}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    title,"}, {"type": "INSERT", "lineNumber": 43, "content": "                    style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 49, "content": "                    //back"}, {"type": "INSERT", "lineNumber": 50, "content": "                  "}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                    "}]}, {"timestamp": 1757273333536, "changes": [{"type": "MODIFY", "lineNumber": 43, "content": "                    style: AppTextStyles.title.copyWith(", "oldContent": "                    style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 50, "content": "                    IconButton("}, {"type": "INSERT", "lineNumber": 51, "content": "                    onPressed: () => context.back(),"}, {"type": "INSERT", "lineNumber": 52, "content": "                    icon: const <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 53, "content": "                      Icons.arrow_back_ios,"}, {"type": "INSERT", "lineNumber": 54, "content": "                      color: Colors.white,"}, {"type": "INSERT", "lineNumber": 55, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 56, "content": "                  ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                  "}]}, {"timestamp": 1757273336335, "changes": [{"type": "MODIFY", "lineNumber": 12, "content": "    this.title,", "oldContent": "     this.title,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  if(title != null)"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                  Text("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                    style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                      color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                      fontSize: 32,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                  )else "}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                    //back"}, {"type": "INSERT", "lineNumber": 40, "content": "                  if (title != null)"}, {"type": "INSERT", "lineNumber": 41, "content": "                    Text("}, {"type": "INSERT", "lineNumber": 42, "content": "                      title!,"}, {"type": "INSERT", "lineNumber": 43, "content": "                      style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 44, "content": "                        color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 45, "content": "                        fontSize: 32,"}, {"type": "INSERT", "lineNumber": 46, "content": "                        fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 47, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 48, "content": "                    )"}, {"type": "INSERT", "lineNumber": 49, "content": "                  else"}, {"type": "INSERT", "lineNumber": 51, "content": "                      onPressed: () => context.back(),"}, {"type": "INSERT", "lineNumber": 52, "content": "                      icon: const <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 53, "content": "                        Icons.arrow_back_ios,"}, {"type": "INSERT", "lineNumber": 54, "content": "                        color: Colors.white,"}, {"type": "INSERT", "lineNumber": 55, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 56, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                    onPressed: () => context.back(),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                    icon: const <PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                      Icons.arrow_back_ios,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "                  ),"}]}, {"timestamp": 1757273355478, "changes": [{"type": "INSERT", "lineNumber": 9, "content": "  final "}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                  // Logo"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                  Assets.images.logoSymbol.image("}, {"type": "INSERT", "lineNumber": 58, "content": "                  // Logo"}, {"type": "INSERT", "lineNumber": 59, "content": "                  Assets.images.logoSymbol.image("}]}, {"timestamp": 1757273365692, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "  final bool with<PERSON><PERSON><PERSON><PERSON><PERSON>;", "oldContent": "  final "}, {"type": "INSERT", "lineNumber": 14, "content": "    this.withBackButton = false,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                  // Logo"}, {"type": "INSERT", "lineNumber": 59, "content": "                  // Logo"}]}, {"timestamp": 1757273374909, "changes": [{"type": "DELETE", "lineNumber": 50, "oldContent": "                    )"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                  else"}, {"type": "INSERT", "lineNumber": 50, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 51, "content": ""}, {"type": "INSERT", "lineNumber": 52, "content": "                  if (with<PERSON><PERSON><PERSON><PERSON><PERSON>)"}]}, {"timestamp": 1757273392556, "changes": [{"type": "INSERT", "lineNumber": 42, "content": "                  if (with<PERSON><PERSON><PERSON><PERSON><PERSON>)"}, {"type": "INSERT", "lineNumber": 43, "content": "                    IconButton("}, {"type": "INSERT", "lineNumber": 44, "content": "                      onPressed: () => context.back(),"}, {"type": "INSERT", "lineNumber": 45, "content": "                      icon: const <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 46, "content": "                        Icons.arrow_back_ios,"}, {"type": "INSERT", "lineNumber": 47, "content": "                        color: Colors.white,"}, {"type": "INSERT", "lineNumber": 48, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 49, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": ""}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                  if (with<PERSON><PERSON><PERSON><PERSON><PERSON>)"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                    IconButton("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                      onPressed: () => context.back(),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                      icon: const <PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                        Icons.arrow_back_ios,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                        color: Colors.white,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                  // Logo"}]}, {"timestamp": 1757273402578, "changes": [{"type": "DELETE", "lineNumber": 43, "oldContent": "                  if (title != null)"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                    Text("}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                      title!,"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                      style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                        color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                        fontSize: 32,"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 50, "content": "                  if (title != null)"}, {"type": "INSERT", "lineNumber": 51, "content": "                    Text("}, {"type": "INSERT", "lineNumber": 52, "content": "                      title!,"}, {"type": "INSERT", "lineNumber": 53, "content": "                      style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 54, "content": "                        color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 55, "content": "                        fontSize: withBackButton ? 24 : 32,"}, {"type": "INSERT", "lineNumber": 56, "content": "                        fontWeight: FontWeight.bold,"}]}, {"timestamp": 1757273408393, "changes": [{"type": "DELETE", "lineNumber": 46, "oldContent": "                  if (title != null)"}, {"type": "INSERT", "lineNumber": 47, "content": "                        color: Colors.white,"}, {"type": "INSERT", "lineNumber": 48, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 49, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 50, "content": "                  if (title != null)"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                        color: Colors.white,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                      ),"}, {"type": "MODIFY", "lineNumber": 55, "content": "                        fontSize: withBackButton ? 12 : 32,", "oldContent": "                        fontSize: withBackButton ? 24 : 32,"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                    ),"}]}, {"timestamp": 1757273423896, "changes": [{"type": "MODIFY", "lineNumber": 47, "content": "                        color: Colors.white,", "oldContent": "                        color: Colors.white,"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                    Text("}, {"type": "MODIFY", "lineNumber": 51, "content": "                    Text(", "oldContent": "                      title!,"}, {"type": "INSERT", "lineNumber": 52, "content": "                      title!,"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                        fontSize: withBackButton ? 24 : 32,"}, {"type": "INSERT", "lineNumber": 56, "content": "                        fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 58, "content": "                      textAlign: TextAlign.center,"}]}, {"timestamp": 1757273426699, "changes": [{"type": "INSERT", "lineNumber": 46, "content": "                        Icons.arrow_back_ios,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                        color: Colors.white,"}, {"type": "INSERT", "lineNumber": 50, "content": "                  if (title != null)"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                  if (title != null)"}, {"type": "MODIFY", "lineNumber": 56, "content": "                        fontWeight: FontWeight.bold,", "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "MODIFY", "lineNumber": 58, "content": "                      textAlign: TextAlign.right,", "oldContent": "                      textAlign: TextAlign.center,"}]}, {"timestamp": 1757273434509, "changes": [{"type": "INSERT", "lineNumber": 55, "content": "                        fontSize: withBackButton ? 12 : 32,"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                      textAlign: TextAlign.right,"}]}, {"timestamp": 1757273443669, "changes": [{"type": "DELETE", "lineNumber": 42, "oldContent": "                  if (with<PERSON><PERSON><PERSON><PERSON><PERSON>)"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                    IconButton("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                      onPressed: () => context.back(),"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "                      icon: const <PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "                        Icons.arrow_back_ios,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "                        color: Colors.white,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "                  if (title != null)"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "                    Text("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "                      title!,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "                      style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "                        color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                        fontSize: withBackButton ? 12 : 32,"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                        fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                    ),"}, {"type": "INSERT", "lineNumber": 42, "content": "                  Row("}, {"type": "INSERT", "lineNumber": 43, "content": "                    children: ["}, {"type": "INSERT", "lineNumber": 44, "content": "                      if (with<PERSON><PERSON><PERSON><PERSON><PERSON>)"}, {"type": "INSERT", "lineNumber": 45, "content": "                        IconButton("}, {"type": "INSERT", "lineNumber": 46, "content": "                          onPressed: () => context.back(),"}, {"type": "INSERT", "lineNumber": 47, "content": "                          icon: const <PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 48, "content": "                            Icons.arrow_back_ios,"}, {"type": "INSERT", "lineNumber": 49, "content": "                            color: Colors.white,"}, {"type": "INSERT", "lineNumber": 50, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 51, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 52, "content": "                      if (title != null)"}, {"type": "INSERT", "lineNumber": 53, "content": "                        Text("}, {"type": "INSERT", "lineNumber": 54, "content": "                          title!,"}, {"type": "INSERT", "lineNumber": 55, "content": "                          style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 56, "content": "                            color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 57, "content": "                            fontSize: withBackButton ? 12 : 32,"}, {"type": "INSERT", "lineNumber": 58, "content": "                            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 59, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 60, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 61, "content": "                    ],"}, {"type": "INSERT", "lineNumber": 62, "content": "                  ),"}]}, {"timestamp": 1757273457672, "changes": [{"type": "MODIFY", "lineNumber": 57, "content": "                            fontSize: withBackButton ? 24 : 32,", "oldContent": "                            fontSize: withBackButton ? 12 : 32,"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                  Assets.images.logoSymbol.image("}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                    width: 50,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                    height: 50,"}, {"type": "INSERT", "lineNumber": 63, "content": "                  Assets.images.logoSymbol.image("}, {"type": "INSERT", "lineNumber": 64, "content": "                    width: 50,"}, {"type": "INSERT", "lineNumber": 65, "content": "                    height: 50,"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/login.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/login.screen.dart", "baseContent": "import 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_form_builder/flutter_form_builder.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\n\nimport '../../../../core/shared/widgets/app_bar/base_header.widget.dart';\nimport 'widgets/login_fields_container.widget.dart';\nimport 'widgets/login_header.widget.dart';\n\nclass LoginScreen extends HookConsumerWidget {\n  const LoginScreen({\n    super.key,\n  });\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());\n\n    return Directionality(\n      textDirection: TextDirection.rtl,\n      child: Scaffold(\n        backgroundColor: Colors.white,\n        body: SingleChildScrollView(\n          child: FormBuilder(\n            key: form<PERSON><PERSON>,\n            child: Column(\n              children: [\n                // Header\n                BaseHeaderWidget(\n                  title: context.tr.loginTitle,\n                ),\n\n                // Form Content\n                LoginFieldsContainer(\n                  formKey: formKey,\n                ),\n              ],\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1757271901560, "deltas": [{"timestamp": 1757272235348, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "        body: SingleChildScrollView("}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 25, "oldContent": "            key: <PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 28, "oldContent": "                // Header"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "                BaseHeaderWidget("}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                  title: context.tr.loginTitle,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                ),"}, {"type": "INSERT", "lineNumber": 23, "content": "        body: FormBuilder("}, {"type": "INSERT", "lineNumber": 24, "content": "          key: <PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 25, "content": "          child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 26, "content": "            children: ["}, {"type": "INSERT", "lineNumber": 27, "content": "              // Header"}, {"type": "INSERT", "lineNumber": 28, "content": "              BaseHeaderWidget("}, {"type": "INSERT", "lineNumber": 29, "content": "                title: context.tr.loginTitle,"}, {"type": "INSERT", "lineNumber": 30, "content": "              ),"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                // Form Content"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                LoginFieldsContainer("}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                  formKey: formKey,"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "            ),"}, {"type": "INSERT", "lineNumber": 32, "content": "              // Form Content"}, {"type": "INSERT", "lineNumber": 33, "content": "              LoginFieldsContainer("}, {"type": "INSERT", "lineNumber": 34, "content": "                formKey: formKey,"}, {"type": "INSERT", "lineNumber": 35, "content": "              ),"}, {"type": "INSERT", "lineNumber": 36, "content": "            ],"}]}, {"timestamp": 1757272248299, "changes": [{"type": "DELETE", "lineNumber": 31, "oldContent": "              // Form Content"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "              LoginFieldsContainer("}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                formKey: formKey,"}, {"type": "INSERT", "lineNumber": 32, "content": "              // Form Content"}, {"type": "INSERT", "lineNumber": 33, "content": "              Expanded("}, {"type": "INSERT", "lineNumber": 34, "content": "                child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 35, "content": "                  formKey: formKey,"}, {"type": "INSERT", "lineNumber": 36, "content": "                ),"}]}, {"timestamp": 1757272374313, "changes": [{"type": "DELETE", "lineNumber": 8, "oldContent": "import 'widgets/login_header.widget.dart';"}, {"type": "MODIFY", "lineNumber": 31, "content": "              // Form Content", "oldContent": "              // Form Content"}, {"type": "MODIFY", "lineNumber": 36, "content": "              ),", "oldContent": "              ),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/widgets/login_fields_container.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/widgets/login_fields_container.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_form_builder/flutter_form_builder.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:dropx/src/core/consts/network/api_strings.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/fields/text_field.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/auth/providers/auth_providers.dart';\nimport 'package:dropx/src/screens/auth/view/login/widgets/login_buttons.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass LoginFieldsContainer extends HookConsumerWidget {\n  final GlobalKey<FormBuilderState> formKey;\n\n  const LoginFieldsContainer({super.key, required this.formKey});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final authController = ref.watch(authControllerNotifierProvider);\n    final rememberMe = useState(false);\n    final isFormValid = useState(false);\n\n    // Form validation state\n    final phoneValid = useState(false);\n    final passwordValid = useState(false);\n\n    // Update form validity\n    useEffect(() {\n      isFormValid.value = phoneValid.value && passwordValid.value;\n      return null;\n    }, [phoneValid.value, passwordValid.value]);\n\n    void login() {\n      if (!formKey.currentState!.saveAndValidate()) return;\n\n      final data = formKey.currentState?.instantValue ?? {};\n\n      authController.login(data: data);\n    }\n\n    return Padding(\n      padding: const EdgeInsets.symmetric(horizontal: 32.0),\n      child: Column(\n        crossAxisAlignment: CrossAxisAlignment.stretch,\n        children: [\n          Spa\n          AppGaps.gap16,\n\n          // Phone Number Field\n          BaseTextField(\n            name: FieldsConsts.mobile,\n            title: context.tr.mobileNumber,\n            hint: context.tr.phoneHint,\n            textInputType: TextInputType.phone,\n            useUnderlineBorder: true,\n            validator: (value) => Validations.palestinianPhoneNumber(\n              value,\n              emptyMessage: context.tr.mobileNumber,\n              invalidMessage: context.tr.invalidPhoneNumber,\n            ),\n            realTimeValidator: (value) {\n              final error = Validations.palestinianPhoneNumber(\n                value,\n                emptyMessage: context.tr.mobileNumber,\n                invalidMessage: context.tr.invalidPhoneNumber,\n              );\n              phoneValid.value = error == null;\n              return error;\n            },\n          ),\n\n          AppGaps.gap16,\n\n          // Password Field\n          BaseTextField(\n            name: FieldsConsts.password,\n            title: context.tr.password,\n            hint: context.tr.password,\n            textInputType: TextInputType.visiblePassword,\n            isObscure: true,\n            useUnderlineBorder: true,\n            validator: (value) => Validations.password(\n              value,\n              emptyPasswordMessage: context.tr.password,\n            ),\n            realTimeValidator: (value) {\n              final error = Validations.password(\n                value,\n                emptyPasswordMessage: context.tr.password,\n              );\n              passwordValid.value = error == null;\n              return error;\n            },\n          ),\n\n          AppGaps.gap16,\n\n          // Remember Me & Forgot Password\n          Row(\n            mainAxisAlignment: MainAxisAlignment.spaceBetween,\n            children: [\n              Row(\n                children: [\n                  SizedBox(\n                    width: 24,\n                    height: 24,\n                    child: Checkbox(\n                      value: rememberMe.value,\n                      onChanged: (value) {\n                        rememberMe.value = value ?? false;\n                      },\n                      activeColor: ColorManager.primaryColor,\n                      side: const BorderSide(color: Colors.black, width: 1.5),\n                      shape: RoundedRectangleBorder(\n                        borderRadius: BorderRadius.circular(4),\n                      ),\n                    ),\n                  ),\n                  AppGaps.gap8,\n                  Text(\n                    context.tr.rememberMe,\n                    style: AppTextStyles.labelMedium.copyWith(fontSize: 13),\n                  ),\n                ],\n              ),\n              TextButton(\n                onPressed: () {\n                  // Handle forgot password\n                },\n                child: Text(\n                  context.tr.forgotPasswordLink,\n                  style: AppTextStyles.labelMedium.copyWith(\n                    color: ColorManager.termsLinkColor,\n                    fontSize: 13,\n                  ),\n                ),\n              ),\n            ],\n          ),\n\n          AppGaps.gap24,\n\n          // Buttons\n          LoginButtons(\n            isLoading: authController.isLoading,\n            isFormValid: isFormValid.value,\n            onLogin: login,\n          ),\n\n          AppGaps.gap16,\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1757272216350, "deltas": [{"timestamp": 1757272218690, "changes": [{"type": "MODIFY", "lineNumber": 46, "content": "          Spacer(),", "oldContent": "          Spa"}]}, {"timestamp": 1757272225011, "changes": [{"type": "MODIFY", "lineNumber": 96, "content": "          Spacer(),", "oldContent": "          AppGaps.gap16,"}]}, {"timestamp": 1757272228666, "changes": [{"type": "DELETE", "lineNumber": 96, "oldContent": "          Spacer(),"}]}, {"timestamp": 1757272235372, "changes": [{"type": "DELETE", "lineNumber": 96, "oldContent": ""}]}, {"timestamp": 1757272255065, "changes": [{"type": "INSERT", "lineNumber": 140, "content": "          Spacer(),"}]}, {"timestamp": 1757272258481, "changes": [{"type": "INSERT", "lineNumber": 148, "content": "          Spacer(),"}]}, {"timestamp": 1757272264631, "changes": [{"type": "MODIFY", "lineNumber": 140, "content": "          Spacer(", "oldContent": "          Spacer(),"}, {"type": "INSERT", "lineNumber": 141, "content": "            flex: 2,"}, {"type": "INSERT", "lineNumber": 142, "content": "          ),"}]}, {"timestamp": 1757272267731, "changes": [{"type": "MODIFY", "lineNumber": 143, "content": "", "oldContent": ""}, {"type": "DELETE", "lineNumber": 151, "oldContent": ""}, {"type": "DELETE", "lineNumber": 152, "oldContent": "          AppGaps.gap16,"}]}, {"timestamp": 1757272271303, "changes": [{"type": "MODIFY", "lineNumber": 141, "content": "            flex: 5,", "oldContent": "            flex: 2,"}, {"type": "INSERT", "lineNumber": 142, "content": "          ),"}, {"type": "DELETE", "lineNumber": 143, "oldContent": ""}]}, {"timestamp": 1757272279334, "changes": [{"type": "DELETE", "lineNumber": 140, "oldContent": "          Spacer("}, {"type": "DELETE", "lineNumber": 141, "oldContent": "            flex: 5,"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 140, "content": "          Spacer(),"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "          Spacer(),"}, {"type": "INSERT", "lineNumber": 148, "content": "          AppGaps.gap24,"}]}, {"timestamp": 1757272283530, "changes": [{"type": "DELETE", "lineNumber": 146, "oldContent": "          AppGaps.gap24,"}, {"type": "INSERT", "lineNumber": 148, "content": "          AppGaps.gap48,"}]}, {"timestamp": 1757272306392, "changes": [{"type": "INSERT", "lineNumber": 106, "content": "                      checkColor: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "          AppGaps.gap48,"}, {"type": "INSERT", "lineNumber": 149, "content": "          AppGaps.gap48,"}]}, {"timestamp": 1757272315150, "changes": [{"type": "DELETE", "lineNumber": 119, "oldContent": "                  Text("}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                    context.tr.remember<PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                    style: AppTextStyles.labelMedium.copyWith(fontSize: 13),"}, {"type": "INSERT", "lineNumber": 119, "content": "                  GestureDetector("}, {"type": "INSERT", "lineNumber": 120, "content": "                    onTap: () {"}, {"type": "INSERT", "lineNumber": 121, "content": "                      rememberMe.value = !rememberMe.value;"}, {"type": "INSERT", "lineNumber": 122, "content": "                    },"}, {"type": "INSERT", "lineNumber": 123, "content": "                    child: Text("}, {"type": "INSERT", "lineNumber": 124, "content": "                      context.tr.remember<PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 125, "content": "                      style: AppTextStyles.labelMedium.copyWith(fontSize: 13),"}, {"type": "INSERT", "lineNumber": 126, "content": "                    ),"}]}, {"timestamp": 1757272594341, "changes": [{"type": "INSERT", "lineNumber": 96, "content": "          AppGaps.gap8,"}, {"type": "INSERT", "lineNumber": 97, "content": ""}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "                ],"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "              ),"}, {"type": "MODIFY", "lineNumber": 129, "content": "                  ),", "oldContent": "              TextButton("}, {"type": "INSERT", "lineNumber": 130, "content": "                ],"}, {"type": "INSERT", "lineNumber": 131, "content": "              ),"}, {"type": "INSERT", "lineNumber": 132, "content": "              TextButton("}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/widgets/login_buttons.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/login/widgets/login_buttons.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/auth/view/register/register.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass LoginButtons extends StatelessWidget {\n  final bool isLoading;\n  final bool isFormValid;\n  final VoidCallback onLogin;\n\n  const LoginButtons({\n    super.key,\n    required this.isLoading,\n    required this.isFormValid,\n    required this.onLogin,\n  });\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      children: [\n        // Login Button\n        SizedBox(\n          height: 50,\n          width: double.infinity,\n          child: Button(\n            onPressed: isFormValid && !isLoading ? onLogin : null,\n            style: ElevatedButton.styleFrom(\n              backgroundColor: isFormValid\n                  ? ColorManager.primaryColor\n                  : ColorManager.disabledButtonColor,\n              foregroundColor: isFormValid ? ColorManager.white : Colors.black,\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(12),\n              ),\n              elevation: 0,\n            ),\n            child: isLoading\n                ? const SizedBox(\n                    height: 20,\n                    width: 20,\n                    child: CircularProgressIndicator(\n                      strokeWidth: 2,\n                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),\n                    ),\n                  )\n                : Text(\n                    context.tr.login,\n                    style: AppTextStyles.labelLarge.copyWith(\n                      fontSize: 18,\n                      fontWeight: FontWeight.bold,\n                    ),\n                  ),\n          ),\n        ),\n\n        AppGaps.gap16,\n\n        // Don't Have Account Text\n        Text(\n          context.tr.dontHaveAccount,\n          textAlign: TextAlign.center,\n          style: AppTextStyles.labelMedium.copyWith(\n            color: Colors.black54,\n            fontSize: 14,\n          ),\n        ),\n\n        AppGaps.gap8,\n\n        // Register Button\n        SizedBox(\n          height: 50,\n          width: double.infinity,\n          child: OutlinedButton(\n            onPressed: () {\n              const RegisterScreen().navigate;\n            },\n            style: OutlinedButton.styleFrom(\n              foregroundColor: ColorManager.primaryColor,\n              side: const BorderSide(\n                color: ColorManager.primaryColor,\n                width: 1.5,\n              ),\n              shape: RoundedRectangleBorder(\n                borderRadius: BorderRadius.circular(12),\n              ),\n            ),\n            child: Text(\n              context.tr.createAccount,\n              style: AppTextStyles.labelLarge.copyWith(\n                fontSize: 18,\n                fontWeight: FontWeight.bold,\n                color: ColorManager.primaryColor,\n              ),\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n}\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 28, "oldContent": "            style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              backgroundColor: isFormValid"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                  ? ColorManager.primaryColor"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                  : ColorManager.disabledButtonColor,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "              foregroundColor: isFormValid ? ColorManager.white : Colors.black,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "              shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "              elevation: 0,"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "            ),"}, {"type": "INSERT", "lineNumber": 28, "content": "           "}]}, {"timestamp": 1757272353357, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "            label: isLoading", "oldContent": "            child: is<PERSON><PERSON>ding"}]}, {"timestamp": 1757272360061, "changes": [{"type": "DELETE", "lineNumber": 27, "oldContent": "            onPressed: isFormValid && !isLoading ? onLogin : null,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "           "}, {"type": "DELETE", "lineNumber": 29, "oldContent": "            label: isLoading"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "                ? const SizedBox("}, {"type": "DELETE", "lineNumber": 31, "oldContent": "                    height: 20,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "                    width: 20,"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                    child: CircularProgressIndicator("}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                      strokeWidth: 2,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                : Text("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                    context.tr.login,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                    style: AppTextStyles.labelLarge.copyWith("}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                      fontSize: 18,"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 27, "content": "              onPressed: isFormValid && !isLoading ? onLogin : null,"}, {"type": "INSERT", "lineNumber": 28, "content": "              label: context.tr.login),"}]}, {"timestamp": 1757272393619, "changes": [{"type": "INSERT", "lineNumber": 26, "content": "          "}]}, {"timestamp": 1757272396108, "changes": [{"type": "INSERT", "lineNumber": 29, "content": "              isLoading: isLoading,"}, {"type": "INSERT", "lineNumber": 30, "content": "              loadingWidget: LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 31, "content": "              color: isFormValid"}, {"type": "INSERT", "lineNumber": 32, "content": "                  ? ColorManager.primaryColor"}, {"type": "INSERT", "lineNumber": 33, "content": "                  : ColorManager.disabledButtonColor,"}]}, {"timestamp": 1757272400591, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../../../../core/shared/widgets/loading/loading_widget.dart';"}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "DELETE", "lineNumber": 26, "oldContent": "          "}, {"type": "DELETE", "lineNumber": 28, "oldContent": "              onPressed: isFormValid && !isLoading ? onLogin : null,"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              isLoading: isLoading,"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "              label: context.tr.login),"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "              loadingWidget: LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 29, "content": "            onPressed: isFormValid && !isLoading ? onLogin : null,"}, {"type": "INSERT", "lineNumber": 30, "content": "            isLoading: isLoading,"}, {"type": "INSERT", "lineNumber": 31, "content": "            loadingWidget: LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 32, "content": "            color: isFormValid"}, {"type": "INSERT", "lineNumber": 33, "content": "                ? ColorManager.primaryColor"}, {"type": "INSERT", "lineNumber": 34, "content": "                : ColorManager.disabledButtonColor,"}, {"type": "INSERT", "lineNumber": 35, "content": "            label: context.tr.login,"}, {"type": "INSERT", "lineNumber": 36, "content": "          ),"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "              color: isFormValid"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                  ? ColorManager.primaryColor"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                  : ColorManager.disabledButtonColor,"}]}, {"timestamp": 1757272479563, "changes": [{"type": "MODIFY", "lineNumber": 8, "content": "class LoginButtons extends StatelessWidget {", "oldContent": "class LoginButtons extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 36, "oldContent": ""}, {"type": "INSERT", "lineNumber": 37, "content": "        ),"}, {"type": "INSERT", "lineNumber": 38, "content": ""}]}, {"timestamp": 1757273256748, "changes": [{"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": "class LoginButtons extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "            color: isFormValid"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                ? ColorManager.primaryColor"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                : ColorManager.disabledButtonColor,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 37, "oldContent": ""}, {"type": "INSERT", "lineNumber": 34, "content": "        ),"}, {"type": "INSERT", "lineNumber": 35, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/lib/src/utils/validations.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/lib/src/utils/validations.dart", "baseContent": "part of xr_helper;\n\nclass Validations {\n  //! التحقق من كلمة المرور\n  static String? password(\n      String? value, {\n        String? emptyPasswordMessage = \"كلمة المرور لا يمكن أن تكون فارغة\",\n        String? passwordLengthMessage =\n        \"يجب أن تكون كلمة المرور 8 أحرف على الأقل\",\n      }) {\n    if (value == null || value.isEmpty) {\n      return emptyPasswordMessage;\n    } else if (value.length < 8) {\n      return passwordLengthMessage;\n    }\n    return null;\n  }\n\n  //! التحقق من رقم الهاتف\n  static String? phoneNumber(\n      value, {\n        String? emptyPhoneMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\",\n        String? phoneLengthMessage = \"رقم الهاتف غير صالح\",\n      }) {\n    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';\n    RegExp regExp = RegExp(pattern);\n    if (value == null || value.isEmpty) {\n      return emptyPhoneMessage;\n    } else if (!regExp.hasMatch(value)) {\n      return phoneLengthMessage;\n    }\n    return null;\n  }\n\n  //! التحقق من الأرقام فقط\n  static String? numbersOnly(\n      value, {\n        String? emptyMessage = \"الحقل لا يمكن أن يكون فارغًا\",\n        String? invalidMessage = \"الرقم غير صالح\",\n      }) {\n    String pattern = r'(^[0-9]*$)';\n    RegExp regExp = RegExp(pattern);\n    if (value == null || value.isEmpty) {\n      return emptyMessage;\n    } else if (!regExp.hasMatch(value)) {\n      return invalidMessage;\n    }\n    return null;\n  }\n\n  //! التحقق من البريد الإلكتروني\n  static String? email(\n      String? value, {\n        String? emptyEmailMessage = \"البريد الإلكتروني لا يمكن أن يكون فارغًا\",\n        String? invalidEmailMessage = \"البريد الإلكتروني غير صالح\",\n      }) {\n    final RegExp urlExp = RegExp(\n        r\"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\\.[a-zA-Z]+\");\n    if (value == null || value.isEmpty) {\n      return emptyEmailMessage;\n    } else if (!urlExp.hasMatch(value)) {\n      return invalidEmailMessage;\n    }\n    return null;\n  }\n\n  //! التحقق من أن الحقل غير فارغ\n  static String? mustBeNotEmpty(String? value, {String? emptyMessage}) {\n    if (value == null || value.isEmpty) {\n      return emptyMessage ?? \"الحقل لا يمكن أن يكون فارغًا\";\n    }\n    return null;\n  }\n\n  //! التحقق من الرقم الوطني الفلسطيني\n  static String? palestinianId(\n      String? value, {\n        String? emptyMessage = \"رقم الهوية لا يمكن أن يكون فارغًا\",\n        String? invalidMessage = \"رقم الهوية غير صالح\",\n      }) {\n    if (value == null || value.isEmpty) {\n      return emptyMessage;\n    }\n\n    if (value.length != 9 || !RegExp(r'^[0-9]+$').hasMatch(value)) {\n      return invalidMessage;\n    }\n\n    int sum = 0;\n    for (int i = 0; i < value.length; i++) {\n      int digit = int.parse(value[i]);\n      int incNum = digit * ((i % 2) + 1);\n      sum += (incNum > 9) ? incNum - 9 : incNum;\n    }\n\n    if (sum % 10 != 0) {\n      return invalidMessage;\n    }\n\n    return null;\n  }\n\n  //! التحقق من رقم الهاتف الفلسطيني\n  static String? palestinianPhoneNumber(\n      String? value, {\n        String? emptyMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\",\n        String? invalidMessage = \"رقم الهاتف غير صالح\",\n      }) {\n    if (value == null || value.isEmpty) {\n      return emptyMessage;\n    }\n\n    // أنماط أرقام الهواتف الفلسطينية\n    List<String> patterns = [\n      r'^(00972|0|\\+972)[5][0-9]{8}$', // موبايل مع كود 972\n      r'^(00970|0|\\+970)[5][0-9]{8}$', // موبايل مع كود 970\n      r'^(05[0-9]|0[12346789])([0-9]{7})$', // محلي\n      r'^(00972|0|\\+972|0|)[2][0-9]{7}$', // خط أرضي\n    ];\n\n    bool isValid = false;\n    for (String pattern in patterns) {\n      if (RegExp(pattern).hasMatch(value)) {\n        isValid = true;\n        break;\n      }\n    }\n\n    if (!isValid) {\n      return invalidMessage;\n    }\n\n    return null;\n  }\n\n  //! التحقق من تأكيد كلمة المرور\n  static String? confirmPassword(\n      String? value,\n      String? originalPassword, {\n        String? emptyMessage = \"تأكيد كلمة المرور لا يمكن أن يكون فارغًا\",\n        String? mismatchMessage = \"كلمتا المرور غير متطابقتين\",\n      }) {\n    if (value == null || value.isEmpty) {\n      return emptyMessage;\n    }\n\n    if (value != originalPassword) {\n      return mismatchMessage;\n    }\n\n    return null;\n  }\n}\n", "baseTimestamp": 1757272919626, "deltas": [{"timestamp": 1757272923337, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": "      String? value, {"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "        String? emptyPasswordMessage = \"كلمة المرور لا يمكن أن تكون فارغة\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "        String? passwordLengthMessage ="}, {"type": "DELETE", "lineNumber": 8, "oldContent": "        \"يجب أن تكون كلمة المرور 8 أحرف على الأقل\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 5, "content": "    String? value, {"}, {"type": "INSERT", "lineNumber": 6, "content": "    String? emptyPasswordMessage = \"كلمة المرور لا يمكن أن تكون فارغة\","}, {"type": "INSERT", "lineNumber": 7, "content": "    String? passwordLengthMessage = \"يجب أن تكون كلمة المرور 8 أحرف على الأقل\","}, {"type": "INSERT", "lineNumber": 8, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      value, {"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "        String? emptyPhoneMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "        String? phoneLengthMessage = \"رقم الهاتف غير صالح\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 19, "content": "    value, {"}, {"type": "INSERT", "lineNumber": 20, "content": "    String? emptyPhoneMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 21, "content": "    String? phoneLengthMessage = \"رقم الهاتف غير صالح\","}, {"type": "INSERT", "lineNumber": 22, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "      value, {"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        String? emptyMessage = \"الحقل لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        String? invalidMessage = \"الرقم غير صالح\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 35, "content": "    value, {"}, {"type": "INSERT", "lineNumber": 36, "content": "    String? emptyMessage = \"الحقل لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 37, "content": "    String? invalidMessage = \"الرقم غير صالح\","}, {"type": "INSERT", "lineNumber": 38, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "      String? value, {"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        String? emptyEmailMessage = \"البريد الإلكتروني لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        String? invalidEmailMessage = \"البريد الإلكتروني غير صالح\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 51, "content": "    String? value, {"}, {"type": "INSERT", "lineNumber": 52, "content": "    String? emptyEmailMessage = \"البريد الإلكتروني لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 53, "content": "    String? invalidEmailMessage = \"البريد الإلكتروني غير صالح\","}, {"type": "INSERT", "lineNumber": 54, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "      String? value, {"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "        String? emptyMessage = \"رقم الهوية لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 78, "oldContent": "        String? invalidMessage = \"رقم الهوية غير صالح\","}, {"type": "DELETE", "lineNumber": 79, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 75, "content": "    String? value, {"}, {"type": "INSERT", "lineNumber": 76, "content": "    String? emptyMessage = \"رقم الهوية لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 77, "content": "    String? invalidMessage = \"رقم الهوية غير صالح\","}, {"type": "INSERT", "lineNumber": 78, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "      String? value, {"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "        String? emptyMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 106, "oldContent": "        String? invalidMessage = \"رقم الهاتف غير صالح\","}, {"type": "DELETE", "lineNumber": 107, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 103, "content": "    String? value, {"}, {"type": "INSERT", "lineNumber": 104, "content": "    String? emptyMessage = \"رقم الهاتف لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 105, "content": "    String? invalidMessage = \"رقم الهاتف غير صالح\","}, {"type": "INSERT", "lineNumber": 106, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "      String? value,"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "      String? originalPassword, {"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "        String? emptyMessage = \"تأكيد كلمة المرور لا يمكن أن يكون فارغًا\","}, {"type": "DELETE", "lineNumber": 140, "oldContent": "        String? mismatchMessage = \"كلمتا المرور غير متطابقتين\","}, {"type": "DELETE", "lineNumber": 141, "oldContent": "      }) {"}, {"type": "INSERT", "lineNumber": 136, "content": "    String? value,"}, {"type": "INSERT", "lineNumber": 137, "content": "    String? originalPassword, {"}, {"type": "INSERT", "lineNumber": 138, "content": "    String? emptyMessage = \"تأكيد كلمة المرور لا يمكن أن يكون فارغًا\","}, {"type": "INSERT", "lineNumber": 139, "content": "    String? mismatchMessage = \"كلمتا المرور غير متطابقتين\","}, {"type": "INSERT", "lineNumber": 140, "content": "  }) {"}]}, {"timestamp": 1757272928480, "changes": [{"type": "MODIFY", "lineNumber": 19, "content": "    value, {", "oldContent": "    value, {"}, {"type": "MODIFY", "lineNumber": 35, "content": "    value, {", "oldContent": "    value, {"}, {"type": "MODIFY", "lineNumber": 51, "content": "    String? value, {", "oldContent": "    String? value, {"}, {"type": "MODIFY", "lineNumber": 75, "content": "    String? value, {", "oldContent": "    String? value, {"}, {"type": "MODIFY", "lineNumber": 103, "content": "    String? value, {", "oldContent": "    String? value, {"}, {"type": "MODIFY", "lineNumber": 136, "content": "    String? value,", "oldContent": "    String? value,"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/verification/verification.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/verification/verification.screen.dart", "baseContent": "import 'package:dropx/src/core/shared/widgets/app_bar/base_header.widget.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:pinput/pinput.dart';\nimport 'package:google_fonts/google_fonts.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/auth/providers/auth_providers.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/verification_header.widget.dart';\n\nclass VerificationScreen extends HookConsumerWidget {\n  final String phone;\n  final Map<String, dynamic> userData;\n\n  const VerificationScreen({\n    super.key,\n    required this.phone,\n    required this.userData,\n  });\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final authController = ref.watch(authControllerNotifierProvider);\n    final pinController = useTextEditingController();\n    final focusNode = useFocusNode();\n    final isFormValid = useState(false);\n\n    void onPinChanged(String pin) {\n      isFormValid.value = pin.length == 6;\n    }\n\n    void verifyCode() {\n      if (pinController.text.length != 6) return;\n\n      authController.verifyCode(\n        userData: userData,\n        code: pinController.text,\n      );\n    }\n\n    void resendCode() {\n      // Handle resend code logic\n      showToast(context.tr.verificationCodeSent);\n    }\n\n    useEffect(() {\n      pinController.dispose;\n      focusNode.dispose;\n      return null;\n    }, []);\n\n    const borderColor = ColorManager.primaryColor;\n\n    final defaultPinTheme = PinTheme(\n      width: 56,\n      height: 56,\n      textStyle: GoogleFonts.cairo(\n        fontSize: 22,\n        color: Colors.black87,\n        fontWeight: FontWeight.bold,\n      ),\n      decoration: const BoxDecoration(),\n    );\n\n    final cursor = Column(\n      mainAxisAlignment: MainAxisAlignment.end,\n      children: [\n        Container(\n          width: 56,\n          height: 3,\n          decoration: BoxDecoration(\n            color: borderColor,\n            borderRadius: BorderRadius.circular(8),\n          ),\n        ),\n      ],\n    );\n\n    final preFilledWidget = Column(\n      mainAxisAlignment: MainAxisAlignment.end,\n      children: [\n        Container(\n          width: 56,\n          height: 3,\n          decoration: BoxDecoration(\n            color: Colors.grey.withOpacity(0.3),\n            borderRadius: BorderRadius.circular(8),\n          ),\n        ),\n      ],\n    );\n\n    return Directionality(\n      textDirection: TextDirection.rtl,\n      child: Scaffold(\n        backgroundColor: Colors.white,\n        body: SingleChildScrollView(\n          child: Column(\n            children: [\n              // Header\n              BaseHeaderWidget(),\n              \n              // Content\n              Padding(\n                padding: const EdgeInsets.symmetric(horizontal: 32.0),\n                child: Column(\n                  crossAxisAlignment: CrossAxisAlignment.stretch,\n                  children: [\n                    AppGaps.gap24,\n\n                    // Verification Message\n                    Text(\n                      context.tr.verificationMessage,\n                      textAlign: TextAlign.center,\n                      style: AppTextStyles.labelLarge.copyWith(\n                        fontSize: 16,\n                        color: Colors.black87,\n                      ),\n                    ),\n\n                    AppGaps.gap8,\n\n                    // Phone Number\n                    Text(\n                      phone,\n                      textAlign: TextAlign.center,\n                      style: AppTextStyles.title.copyWith(\n                        fontSize: 18,\n                        fontWeight: FontWeight.bold,\n                        color: ColorManager.primaryColor,\n                      ),\n                    ),\n\n                    AppGaps.gap24,\n\n                    // Enter Code Label\n                    Text(\n                      context.tr.enterVerificationCode,\n                      style: AppTextStyles.labelLarge.copyWith(\n                        fontWeight: FontWeight.bold,\n                        fontSize: 16,\n                        color: Colors.black,\n                      ),\n                    ),\n\n                    AppGaps.gap16,\n\n                    // PIN Input\n                    Pinput(\n                      length: 6,\n                      pinAnimationType: PinAnimationType.slide,\n                      controller: pinController,\n                      focusNode: focusNode,\n                      defaultPinTheme: defaultPinTheme,\n                      showCursor: true,\n                      cursor: cursor,\n                      preFilledWidget: preFilledWidget,\n                      onChanged: onPinChanged,\n                      onCompleted: (pin) {\n                        verifyCode();\n                      },\n                    ),\n\n                    AppGaps.gap24,\n\n                    // Verify Button\n                    SizedBox(\n                      height: 50,\n                      width: double.infinity,\n                      child: ElevatedButton(\n                        onPressed: isFormValid.value && !authController.isLoading\n                            ? verifyCode\n                            : null,\n                        style: ElevatedButton.styleFrom(\n                          backgroundColor: isFormValid.value\n                              ? ColorManager.primaryColor\n                              : ColorManager.disabledButtonColor,\n                          foregroundColor: isFormValid.value \n                              ? ColorManager.white \n                              : Colors.black,\n                          shape: RoundedRectangleBorder(\n                            borderRadius: BorderRadius.circular(12),\n                          ),\n                          elevation: 0,\n                        ),\n                        child: authController.isLoading\n                            ? const SizedBox(\n                                height: 20,\n                                width: 20,\n                                child: CircularProgressIndicator(\n                                  strokeWidth: 2,\n                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),\n                                ),\n                              )\n                            : Text(\n                                context.tr.verify,\n                                style: AppTextStyles.labelLarge.copyWith(\n                                  fontSize: 18,\n                                  fontWeight: FontWeight.bold,\n                                ),\n                              ),\n                      ),\n                    ),\n\n                    AppGaps.gap16,\n\n                    // Resend Code\n                    TextButton(\n                      onPressed: resendCode,\n                      child: Text(\n                        context.tr.resendCode,\n                        style: AppTextStyles.labelMedium.copyWith(\n                          color: ColorManager.termsLinkColor,\n                          fontSize: 14,\n                        ),\n                      ),\n                    ),\n\n                    AppGaps.gap16,\n                  ],\n                ),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1757273311162, "deltas": [{"timestamp": 1757273336323, "changes": [{"type": "MODIFY", "lineNumber": 104, "content": "", "oldContent": "              "}, {"type": "DELETE", "lineNumber": 173, "oldContent": "                        onPressed: isFormValid.value && !authController.isLoading"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "                            ? verifyCode"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "                            : null,"}, {"type": "INSERT", "lineNumber": 173, "content": "                        onPressed:"}, {"type": "INSERT", "lineNumber": 174, "content": "                            isFormValid.value && !authController.isLoading"}, {"type": "INSERT", "lineNumber": 175, "content": "                                ? verifyCode"}, {"type": "INSERT", "lineNumber": 176, "content": "                                : null,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "                          foregroundColor: isFormValid.value "}, {"type": "MODIFY", "lineNumber": 181, "content": "                          foregroundColor: isFormValid.value", "oldContent": "                              ? ColorManager.white "}, {"type": "INSERT", "lineNumber": 182, "content": "                              ? ColorManager.white"}, {"type": "MODIFY", "lineNumber": 195, "content": "                                  valueColor: AlwaysStoppedAnimation<Color>(", "oldContent": "                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),"}, {"type": "INSERT", "lineNumber": 196, "content": "                                      Colors.white),"}]}, {"timestamp": 1757273385159, "changes": [{"type": "MODIFY", "lineNumber": 103, "content": "              BaseHeaderWidget(", "oldContent": "              BaseHeaderWidget(),"}, {"type": "INSERT", "lineNumber": 104, "content": "                title: context.tr.verificationTitle,"}, {"type": "INSERT", "lineNumber": 105, "content": "                withBackButton: true,"}, {"type": "INSERT", "lineNumber": 106, "content": "              ),"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),"}, {"type": "INSERT", "lineNumber": 200, "content": "                                ),"}]}, {"timestamp": 1757273472052, "changes": [{"type": "DELETE", "lineNumber": 11, "oldContent": "import 'widgets/verification_header.widget.dart';"}, {"type": "DELETE", "lineNumber": 105, "oldContent": ""}, {"type": "DELETE", "lineNumber": 107, "oldContent": "              // Content"}, {"type": "INSERT", "lineNumber": 106, "content": ""}, {"type": "INSERT", "lineNumber": 107, "content": "              // Content"}, {"type": "INSERT", "lineNumber": 199, "content": "                                ),"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                                ),"}]}, {"timestamp": 1757273484742, "changes": [{"type": "DELETE", "lineNumber": 104, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 106, "content": "", "oldContent": "              // Content"}, {"type": "INSERT", "lineNumber": 107, "content": "              // Content"}, {"type": "MODIFY", "lineNumber": 174, "content": "                      child: <PERSON><PERSON>(", "oldContent": "                      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "MODIFY", "lineNumber": 199, "content": "                                ),", "oldContent": "                                ),"}]}, {"timestamp": 1757273488117, "changes": [{"type": "INSERT", "lineNumber": 105, "content": "              ),"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "              ),"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "                        style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 180, "oldContent": "                          backgroundColor: isFormValid.value"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "                              ? ColorManager.primaryColor"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                              : ColorManager.disabledButtonColor,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "                          foregroundColor: isFormValid.value"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "                              ? ColorManager.white"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "                              : Colors.black,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "                          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 187, "oldContent": "                            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "                          ),"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "                          elevation: 0,"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "                        ),"}, {"type": "INSERT", "lineNumber": 179, "content": "                    "}, {"type": "INSERT", "lineNumber": 187, "content": "                                      Colors.white),"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "                                ),"}]}, {"timestamp": 1757273491741, "changes": [{"type": "DELETE", "lineNumber": 180, "oldContent": "                                      Colors.white),"}, {"type": "MODIFY", "lineNumber": 180, "content": "                        label: authController.isLoading", "oldContent": "                        child: authC<PERSON>roller.isLoading"}, {"type": "INSERT", "lineNumber": 187, "content": "                                      Colors.white),"}]}, {"timestamp": 1757273505019, "changes": [{"type": "DELETE", "lineNumber": 180, "oldContent": "                        label: authController.isLoading"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "                            ? const SizedBox("}, {"type": "DELETE", "lineNumber": 182, "oldContent": "                                height: 20,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "                                width: 20,"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "                                child: CircularProgressIndicator("}, {"type": "DELETE", "lineNumber": 185, "oldContent": "                                  strokeWidth: 2,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "                                      Colors.white),"}, {"type": "DELETE", "lineNumber": 187, "oldContent": "                                  valueColor: AlwaysStoppedAnimation<Color>("}, {"type": "DELETE", "lineNumber": 188, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "                              )"}, {"type": "DELETE", "lineNumber": 190, "oldContent": "                            : Text("}, {"type": "DELETE", "lineNumber": 191, "oldContent": "                                context.tr.verify,"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "                                style: AppTextStyles.labelLarge.copyWith("}, {"type": "DELETE", "lineNumber": 193, "oldContent": "                                  fontSize: 18,"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "                                  fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "                                ),"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "                              ),"}, {"type": "INSERT", "lineNumber": 180, "content": "                        label:  context.tr.verify,"}, {"type": "INSERT", "lineNumber": 181, "content": "                        isLoading: authController.isLoading,"}, {"type": "INSERT", "lineNumber": 182, "content": "                        loadingWidget: LoadingWidget(),"}, {"type": "INSERT", "lineNumber": 183, "content": "                     "}]}, {"timestamp": 1757273507410, "changes": [{"type": "INSERT", "lineNumber": 11, "content": "import '../../../../core/shared/widgets/loading/loading_widget.dart';"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "                    "}, {"type": "MODIFY", "lineNumber": 180, "content": "                        label: context.tr.verify,", "oldContent": "                        label:  context.tr.verify,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "                     "}]}, {"timestamp": 1757273576381, "changes": [{"type": "DELETE", "lineNumber": 191, "oldContent": "                      child: Text("}, {"type": "DELETE", "lineNumber": 192, "oldContent": "                        context.tr.resend<PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "                        style: AppTextStyles.labelMedium.copyWith("}, {"type": "DELETE", "lineNumber": 194, "oldContent": "                          color: ColorManager.termsLinkColor,"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "                          fontSize: 14,"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "                        ),"}, {"type": "INSERT", "lineNumber": 191, "content": "                      child: Row("}, {"type": "INSERT", "lineNumber": 192, "content": "                        children: ["}, {"type": "INSERT", "lineNumber": 193, "content": "                          Text("}, {"type": "INSERT", "lineNumber": 194, "content": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 195, "content": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 196, "content": "                              color: ColorManager.termsLinkColor,"}, {"type": "INSERT", "lineNumber": 197, "content": "                              fontSize: 14,"}, {"type": "INSERT", "lineNumber": 198, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 199, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 200, "content": "                        ],"}]}, {"timestamp": 1757273582693, "changes": [{"type": "DELETE", "lineNumber": 189, "oldContent": "                    TextButton("}, {"type": "DELETE", "lineNumber": 190, "oldContent": "                      onPressed: resendCode,"}, {"type": "DELETE", "lineNumber": 191, "oldContent": "                      child: Row("}, {"type": "DELETE", "lineNumber": 192, "oldContent": "                        children: ["}, {"type": "DELETE", "lineNumber": 193, "oldContent": "                          Text("}, {"type": "INSERT", "lineNumber": 189, "content": "                    Row("}, {"type": "INSERT", "lineNumber": 190, "content": "                      children: ["}, {"type": "INSERT", "lineNumber": 191, "content": "                        Text("}, {"type": "INSERT", "lineNumber": 192, "content": "                          context.tr.didntReceiveCode,"}, {"type": "INSERT", "lineNumber": 193, "content": "                          style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 194, "content": "                            color: Colors.black54,"}, {"type": "INSERT", "lineNumber": 195, "content": "                            fontSize: 14,"}, {"type": "INSERT", "lineNumber": 196, "content": "                          ),"}, {"type": "INSERT", "lineNumber": 197, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 198, "content": "                        TextButton("}, {"type": "INSERT", "lineNumber": 199, "content": "                          onPressed: resendCode,"}, {"type": "INSERT", "lineNumber": 200, "content": "                          child: Text("}, {"type": "DELETE", "lineNumber": 198, "oldContent": "                      ),"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "                    ),"}, {"type": "INSERT", "lineNumber": 207, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 208, "content": "                      ],"}, {"type": "INSERT", "lineNumber": 209, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "                        ],"}]}, {"timestamp": 1757273606626, "changes": [{"type": "DELETE", "lineNumber": 195, "oldContent": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "DELETE", "lineNumber": 199, "oldContent": "                              color: ColorManager.termsLinkColor,"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "                              fontSize: 14,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "                            ),"}, {"type": "INSERT", "lineNumber": 201, "content": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 202, "content": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 203, "content": "                              color: ColorManager.termsLinkColor,"}, {"type": "INSERT", "lineNumber": 204, "content": "                              fontSize: 14,"}, {"type": "INSERT", "lineNumber": 205, "content": "                            ),"}, {"type": "INSERT", "lineNumber": 207, "content": "                        ),"}, {"type": "INSERT", "lineNumber": 208, "content": "                      ],"}, {"type": "INSERT", "lineNumber": 209, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "                        ),"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "                      ],"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "                    ),"}]}, {"timestamp": 1757273638657, "changes": [{"type": "INSERT", "lineNumber": 155, "content": "                      text"}, {"type": "INSERT", "lineNumber": 199, "content": "                        TextButton("}, {"type": "INSERT", "lineNumber": 200, "content": "                          onPressed: resendCode,"}, {"type": "INSERT", "lineNumber": 201, "content": "                          child: Text("}, {"type": "DELETE", "lineNumber": 200, "oldContent": "                        TextButton("}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                          onPressed: resendCode,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "                          child: Text("}, {"type": "DELETE", "lineNumber": 208, "oldContent": ""}, {"type": "DELETE", "lineNumber": 210, "oldContent": "                    AppGaps.gap16,"}, {"type": "INSERT", "lineNumber": 211, "content": ""}, {"type": "INSERT", "lineNumber": 212, "content": "                    AppGaps.gap16,"}]}, {"timestamp": 1757273641386, "changes": [{"type": "MODIFY", "lineNumber": 155, "content": "                      ", "oldContent": "                      text"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 202, "content": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 203, "content": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "MODIFY", "lineNumber": 211, "content": "", "oldContent": ""}]}, {"timestamp": 1757273651019, "changes": [{"type": "DELETE", "lineNumber": 154, "oldContent": "                    Pinput("}, {"type": "DELETE", "lineNumber": 155, "oldContent": "                      "}, {"type": "DELETE", "lineNumber": 156, "oldContent": "                      length: 6,"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "                      pinAnimationType: PinAnimationType.slide,"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "                      controller: <PERSON><PERSON><PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "                      focusNode: focusNode,"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "                      defaultPinTheme: defaultPinTheme,"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "                      showCursor: true,"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "                      cursor: cursor,"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                      preFilledWidget: preFilledWidget,"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                      onChanged: onPinChanged,"}, {"type": "DELETE", "lineNumber": 165, "oldContent": "                      onCompleted: (pin) {"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "                        verifyCode();"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "                      },"}, {"type": "INSERT", "lineNumber": 154, "content": "                    Directionality("}, {"type": "INSERT", "lineNumber": 155, "content": "                      textDirection: TextDirection.ltr,"}, {"type": "INSERT", "lineNumber": 156, "content": "                      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 157, "content": "                        length: 6,"}, {"type": "INSERT", "lineNumber": 158, "content": "                        pinAnimationType: PinAnimationType.slide,"}, {"type": "INSERT", "lineNumber": 159, "content": "                        controller: <PERSON><PERSON><PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 160, "content": "                        focusNode: focusNode,"}, {"type": "INSERT", "lineNumber": 161, "content": "                        defaultPinTheme: defaultPinTheme,"}, {"type": "INSERT", "lineNumber": 162, "content": "                        showCursor: true,"}, {"type": "INSERT", "lineNumber": 163, "content": "                        cursor: cursor,"}, {"type": "INSERT", "lineNumber": 164, "content": "                        preFilledWidget: preFilledWidget,"}, {"type": "INSERT", "lineNumber": 165, "content": "                        onChanged: onPinChanged,"}, {"type": "INSERT", "lineNumber": 166, "content": "                        onCompleted: (pin) {"}, {"type": "INSERT", "lineNumber": 167, "content": "                          verifyCode();"}, {"type": "INSERT", "lineNumber": 168, "content": "                        },"}, {"type": "INSERT", "lineNumber": 169, "content": "                      ),"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 204, "content": "                            context.tr.resend<PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 205, "content": "                            style: AppTextStyles.labelMedium.copyWith("}, {"type": "INSERT", "lineNumber": 212, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 211, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/network/api_endpoints.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/network/api_endpoints.dart", "baseContent": "class ApiEndpoints {\n  static const String url = 'https://dropx.cwgroup.co.il';\n  static const String baseUrl = '$url/api';\n\n  //? Auth\n  static const String login = '$baseUrl/auth/login';\n  static const String register = '$baseUrl/auth/register';\n  static const String verifyCode = '$baseUrl/auth/verify-code';\n  static const String addSocialMedia = '$baseUrl/social/create';\n  static const String deleteSocialMedia = '$baseUrl/social/delete';\n\n  //? APIs\n  static const String stores = '$baseUrl/stores';\n}\n", "baseTimestamp": 1757276796334, "deltas": [{"timestamp": 1757276801895, "changes": [{"type": "DELETE", "lineNumber": 8, "oldContent": "  static const String addSocialMedia = '$baseUrl/social/create';"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  static const String deleteSocialMedia = '$baseUrl/social/delete';"}]}, {"timestamp": 1757276807314, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "  static const String orders = '$baseUrl/orders';", "oldContent": "  static const String stores = '$baseUrl/stores';"}]}, {"timestamp": 1757276816224, "changes": [{"type": "INSERT", "lineNumber": 10, "content": "  static const String stores = '$baseUrl/orders';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/orders/models/orders_response.model.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/orders/models/orders_response.model.dart", "baseContent": "import 'order.model.dart';\n\nclass OrdersResponseModel {\n  final bool success;\n  final String message;\n  final OrdersDataModel data;\n\n  OrdersResponseModel({\n    required this.success,\n    required this.message,\n    required this.data,\n  });\n\n  factory OrdersResponseModel.fromJson(Map<String, dynamic> json) {\n    log\n    return OrdersResponseModel(\n      success: json['success'] as bool,\n      message: json['message'] as String,\n      data: OrdersDataModel.fromJson(json['data'] as Map<String, dynamic>),\n    );\n  }\n\n  Map<String, dynamic> toJson() {\n    return {\n      'success': success,\n      'message': message,\n      'data': data.toJson(),\n    };\n  }\n}\n\nclass OrdersDataModel {\n  final List<OrderModel> created;\n  final List<OrderModel> received;\n\n  OrdersDataModel({\n    required this.created,\n    required this.received,\n  });\n\n  factory OrdersDataModel.fromJson(Map<String, dynamic> json) {\n    return OrdersDataModel(\n      created: (json['created'] as List<dynamic>)\n          .map((e) => OrderModel.fromJson(e as Map<String, dynamic>))\n          .toList(),\n      received: (json['received'] as List<dynamic>)\n          .map((e) => OrderModel.fromJson(e as Map<String, dynamic>))\n          .toList(),\n    );\n  }\n\n  Map<String, dynamic> toJson() {\n    return {\n      'created': created.map((e) => e.toJson()).toList(),\n      'received': received.map((e) => e.toJson()).toList(),\n    };\n  }\n\n  OrdersDataModel copyWith({\n    List<OrderModel>? created,\n    List<OrderModel>? received,\n  }) {\n    return OrdersDataModel(\n      created: created ?? this.created,\n      received: received ?? this.received,\n    );\n  }\n\n  @override\n  bool operator ==(Object other) {\n    if (identical(this, other)) return true;\n    return other is OrdersDataModel &&\n        other.created == created &&\n        other.received == received;\n  }\n\n  @override\n  int get hashCode {\n    return Object.hash(created, received);\n  }\n\n  @override\n  String toString() {\n    return 'OrdersDataModel(created: $created, received: $received)';\n  }\n}\n", "baseTimestamp": 1757278995537, "deltas": [{"timestamp": 1757279003866, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'dart:developer';"}, {"type": "INSERT", "lineNumber": 1, "content": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": "    log"}, {"type": "INSERT", "lineNumber": 16, "content": "    log('asfasfsaf $json');"}, {"type": "INSERT", "lineNumber": 17, "content": ""}]}, {"timestamp": 1757279691354, "changes": [{"type": "INSERT", "lineNumber": 1, "content": ""}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "INSERT", "lineNumber": 16, "content": ""}, {"type": "DELETE", "lineNumber": 17, "oldContent": "    log('asfasfsaf $json');"}, {"type": "DELETE", "lineNumber": 19, "oldContent": ""}]}, {"timestamp": 1757279809405, "changes": [{"type": "DELETE", "lineNumber": 16, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/shipment_card.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/shipment_card.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/orders/models/order.model.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ShipmentCardWidget extends StatelessWidget {\n  final OrderModel order;\n  final VoidCallback? onTap;\n\n  const ShipmentCardWidget({\n    super.key,\n    required this.order,\n    this.onTap,\n  });\n\n  @override\n  Widget build(BuildContext context) {\n    return GestureDetector(\n      onTap: onTap,\n      child: Container(\n        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),\n        padding: EdgeInsets.all(16.w),\n        decoration: BoxDecoration(\n          color: Colors.white,\n          borderRadius: BorderRadius.circular(16.r),\n          border: Border.all(\n            color: ColorManager.lightGrey,\n            width: 1,\n          ),\n          boxShadow: [\n            BoxShadow(\n              color: Colors.black.withOpacity(0.05),\n              spreadRadius: 0,\n              blurRadius: 8,\n              offset: const Offset(0, 2),\n            ),\n          ],\n        ),\n        child: Row(\n          children: [\n            // Package Icon\n            Container(\n              width: 60.w,\n              height: 60.h,\n              decoration: BoxDecoration(\n                color: ColorManager.lightPrimaryColor,\n                borderRadius: BorderRadius.circular(12.r),\n              ),\n              child: Icon(\n                Icons.inventory_2_outlined,\n                color: ColorManager.primaryColor,\n                size: 30.w,\n              ),\n            ),\n\n            AppGaps.gap16,\n\n            // Content\n            Expanded(\n              child: Column(\n                crossAxisAlignment: CrossAxisAlignment.start,\n                children: [\n                  // From - To\n                  Row(\n                    children: [\n                      Text(\n                        context.tr.from,\n                        style: AppTextStyles.bodySmall.copyWith(\n                          color: ColorManager.greyText,\n                        ),\n                      ),\n                      Expanded(\n                        child: Text(\n                          order.fromStation,\n                          style: AppTextStyles.bodyMedium.copyWith(\n                            fontWeight: FontWeight.w600,\n                          ),\n                        ),\n                      ),\n                    ],\n                  ),\n\n                  AppGaps.gap4,\n\n                  Row(\n                    children: [\n                      Text(\n                        context.tr.to,\n                        style: AppTextStyles.bodySmall.copyWith(\n                          color: ColorManager.greyText,\n                        ),\n                      ),\n                      Expanded(\n                        child: Text(\n                          order.toStation,\n                          style: AppTextStyles.bodyMedium.copyWith(\n                            fontWeight: FontWeight.w600,\n                          ),\n                        ),\n                      ),\n                    ],\n                  ),\n\n                  AppGaps.gap8,\n\n                  // Price\n                  Row(\n                    children: [\n                      Text(\n                        context.tr.price,\n                        style: AppTextStyles.bodySmall.copyWith(\n                          color: ColorManager.greyText,\n                        ),\n                      ),\n                      Text(\n                        '${order.price} ${context.tr.points}',\n                        style: AppTextStyles.bodyMedium.copyWith(\n                          fontWeight: FontWeight.bold,\n                          color: ColorManager.primaryColor,\n                        ),\n                      ),\n                    ],\n                  ),\n\n                  AppGaps.gap8,\n\n                  // Status\n                  Container(\n                    padding: EdgeInsets.symmetric(\n                      horizontal: 12.w,\n                      vertical: 4.h,\n                    ),\n                    decoration: BoxDecoration(\n                      color: _getStatusColor(order.status).withOpacity(0.1),\n                      borderRadius: BorderRadius.circular(8.r),\n                      border: Border.all(\n                        color: _getStatusColor(order.status),\n                        width: 1,\n                      ),\n                    ),\n                    child: Text(\n                     \n                          order.statusText ??\n                          order.status,\n                      style: AppTextStyles.bodySmall.copyWith(\n                        color: _getStatusColor(order.status),\n                        fontWeight: FontWeight.w600,\n                      ),\n                    ),\n                  ),\n                ],\n              ),\n            ),\n\n            // Arrow Icon\n            Icon(\n              Icons.arrow_forward_ios,\n              color: ColorManager.greyIcon,\n              size: 16.w,\n            ),\n          ],\n        ),\n      ),\n    );\n  }\n\n  Color _getStatusColor(String status) {\n    switch (status.toLowerCase()) {\n      case 'pending':\n        return Colors.orange;\n      case 'confirmed':\n        return Colors.blue;\n      case 'shipped':\n        return Colors.purple;\n      case 'delivered':\n        return Colors.green;\n      case 'cancelled':\n        return Colors.red;\n      default:\n        return ColorManager.greyText;\n    }\n  }\n}\n", "baseTimestamp": 1757279118202, "deltas": [{"timestamp": 1757279121611, "changes": [{"type": "DELETE", "lineNumber": 143, "oldContent": "                     "}, {"type": "MODIFY", "lineNumber": 143, "content": "                      order.statusText ??", "oldContent": "                          order.statusText ??"}, {"type": "INSERT", "lineNumber": 144, "content": "                          order.latestTracking?.statusText ??"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart", "baseContent": "import 'package:flutter/cupertino.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../../../theme/color_manager.dart';\nimport 'controller/bottom_nav_bar.controller.dart';\n\nclass BottomNavBarWidget extends ConsumerWidget {\n  const BottomNavBarWidget({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final currentIndex = ref.watch(bottomNavigationControllerProvider);\n    final bottomNavCtrl = ref.read(bottomNavControllerProvider);\n\n    final iconSize = 24.w;\n\n    return Container(\n      height: 80.h,\n      margin: EdgeInsets.all(16.w),\n      decoration: BoxDecoration(\n        color: Colors.white,\n        borderRadius: BorderRadius.circular(25.r),\n        border: Border.all(\n          color: ColorManager.primaryColor,\n          width: 2,\n        ),\n        boxShadow: [\n          BoxShadow(\n            color: Colors.black.withOpacity(0.1),\n            spreadRadius: 0,\n            blurRadius: 10,\n            offset: const Offset(0, -1),\n          ),\n        ],\n      ),\n      child: Row(\n        mainAxisAlignment: MainAxisAlignment.spaceAround,\n        children: [\n          _buildNavItem(\n            context: context,\n            icon: CupertinoIcons.house_fill,\n            label: context.tr.homePage,\n            index: 0,\n            currentIndex: currentIndex,\n            iconSize: iconSize,\n            onTap: () => bottomNavCtrl.changeIndex(0),\n          ),\n          _buildNavItem(\n            context: context,\n            icon: Icons.history,\n            label: context.tr.shipmentsHistory,\n            index: 1,\n            currentIndex: currentIndex,\n            iconSize: iconSize,\n            onTap: () => bottomNavCtrl.changeIndex(1),\n          ),\n          // Floating Action Button Space\n          SizedBox(width: 60.w),\n          _buildNavItem(\n            context: context,\n            icon: Icons.account_balance_wallet,\n            label: context.tr.wallet,\n            index: 2,\n            currentIndex: currentIndex,\n            iconSize: iconSize,\n            onTap: () => bottomNavCtrl.changeIndex(2),\n          ),\n          _buildNavItem(\n            context: context,\n            icon: Icons.menu,\n            label: context.tr.menu,\n            index: 3,\n            currentIndex: currentIndex,\n            iconSize: iconSize,\n            onTap: () => bottomNavCtrl.changeIndex(3),\n          ),\n        ],\n      ),\n    );\n  }\n\n  Widget _buildNavItem({\n    required BuildContext context,\n    required IconData icon,\n    required String label,\n    required int index,\n    required int currentIndex,\n    required double iconSize,\n    required VoidCallback onTap,\n  }) {\n    final isSelected = currentIndex == index;\n\n    return GestureDetector(\n      onTap: onTap,\n      child: Container(\n        padding: EdgeInsets.symmetric(\n          horizontal: isSelected ? 16.w : 8.w,\n          vertical: 8.h,\n        ),\n        decoration: BoxDecoration(\n          border: isSelected\n              ? Border.all(color: ColorManager.primaryColor, width: 2)\n              : null,\n          borderRadius: BorderRadius.circular(20.r),\n        ),\n        child: Row(\n          mainAxisSize: MainAxisSize.min,\n          children: [\n            Icon(\n              icon,\n              size: iconSize,\n              color: isSelected ? Colors.white : ColorManager.greyIcon,\n            ),\n            if (isSelected) ...[\n              AppGaps.gap8,\n              Text(\n                label,\n                style: AppTextStyles.bodySmall.copyWith(\n                  color: Colors.white,\n                  fontWeight: FontWeight.w600,\n                ),\n              ),\n            ],\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1757279468930, "deltas": [{"timestamp": 1757279475701, "changes": [{"type": "INSERT", "lineNumber": 104, "content": "          color: isSelected ? ColorManager.primaryColor : Colors.transparent,"}]}, {"timestamp": 1757279478345, "changes": [{"type": "MODIFY", "lineNumber": 104, "content": "          ", "oldContent": "          color: isSelected ? ColorManager.primaryColor : Colors.transparent,"}]}, {"timestamp": 1757279490376, "changes": [{"type": "DELETE", "lineNumber": 104, "oldContent": "          "}, {"type": "MODIFY", "lineNumber": 122, "content": "                  color: ColorManager.primaryColor,", "oldContent": "                  color: Colors.white,"}]}, {"timestamp": 1757279497540, "changes": [{"type": "MODIFY", "lineNumber": 115, "content": "              color: isSelected", "oldContent": "              color: isSelected ? Colors.white : ColorManager.greyIcon,"}, {"type": "INSERT", "lineNumber": 116, "content": "                  ? ColorManager.primaryColor"}, {"type": "INSERT", "lineNumber": 117, "content": "                  : ColorManager.greyIcon,"}, {"type": "INSERT", "lineNumber": 123, "content": "                style: AppTextStyles.bodySmall.copyWith("}, {"type": "DELETE", "lineNumber": 122, "oldContent": "                  color: Colors.white,"}]}, {"timestamp": 1757279516895, "changes": [{"type": "MODIFY", "lineNumber": 107, "content": "          borderRadius: BorderRadius.circular(100.r),", "oldContent": "          borderRadius: BorderRadius.circular(20.r),"}, {"type": "MODIFY", "lineNumber": 118, "content": "            ),", "oldContent": "            ),"}, {"type": "MODIFY", "lineNumber": 124, "content": "                  color: ColorManager.primaryColor,", "oldContent": "                  color: ColorManager.primaryColor,"}]}, {"timestamp": 1757279538435, "changes": [{"type": "DELETE", "lineNumber": 26, "oldContent": "        border: Border.all("}, {"type": "DELETE", "lineNumber": 27, "oldContent": "          color: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "          width: 2,"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 113, "content": "                  : ColorManager.greyIcon,"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "            ),"}, {"type": "INSERT", "lineNumber": 119, "content": "                style: AppTextStyles.bodySmall.copyWith("}, {"type": "DELETE", "lineNumber": 124, "oldContent": "                  color: ColorManager.primaryColor,"}]}, {"timestamp": 1757279542601, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "      margin: EdgeInsets.all(16.w),"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "                  : ColorManager.greyIcon,"}, {"type": "INSERT", "lineNumber": 112, "content": "                  : ColorManager.greyIcon,"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                style: AppTextStyles.bodySmall.copyWith("}, {"type": "INSERT", "lineNumber": 118, "content": "                style: AppTextStyles.bodySmall.copyWith("}]}, {"timestamp": 1757279544636, "changes": [{"type": "INSERT", "lineNumber": 22, "content": "      margin: EdgeInsets.all(16.w),"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "                  : ColorManager.greyIcon,"}, {"type": "INSERT", "lineNumber": 113, "content": "                  : ColorManager.greyIcon,"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "                style: AppTextStyles.bodySmall.copyWith("}, {"type": "INSERT", "lineNumber": 119, "content": "                style: AppTextStyles.bodySmall.copyWith("}]}, {"timestamp": 1757279548574, "changes": [{"type": "DELETE", "lineNumber": 21, "oldContent": "      height: 80.h,"}]}, {"timestamp": 1757279553887, "changes": [{"type": "INSERT", "lineNumber": 22, "content": "      padding: EdgeInsets.all(16.w),"}]}, {"timestamp": 1757279559247, "changes": [{"type": "MODIFY", "lineNumber": 22, "content": "      padding: EdgeInsets.all(8),", "oldContent": "      padding: EdgeInsets.all(16.w),"}]}, {"timestamp": 1757279565457, "changes": [{"type": "DELETE", "lineNumber": 56, "oldContent": "          // Floating Action Button Space"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "          SizedBox(width: 60.w),"}]}, {"timestamp": 1757279574472, "changes": [{"type": "MODIFY", "lineNumber": 25, "content": "        borderRadius: BorderRadius.circular(12.r),", "oldContent": "        borderRadius: BorderRadius.circular(25.r),"}]}]}}}