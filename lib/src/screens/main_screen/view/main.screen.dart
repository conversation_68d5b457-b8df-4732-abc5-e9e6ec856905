import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:dropx/src/screens/home/<USER>/home.screen.dart';

import '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    return Scaffold(
      body: _SelectedScreen(currentIndex: currentIndex),
      bottomNavigationBar: const BottomNavBarWidget(),
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  switch (currentIndex) {
    case 0:
      return context.tr.home;
    case 1:
      return context.tr.reels;
    case 2:
      return context.tr.doctors;
    case 3:
      return context.tr.shops;
  }

  return context.tr.home;
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomeScreen();
    }
    return const SizedBox.shrink();
  }
}
