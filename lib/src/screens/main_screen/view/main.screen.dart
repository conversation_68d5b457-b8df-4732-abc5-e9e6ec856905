import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/home/<USER>/home.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    return Scaffold(
      body: _SelectedScreen(currentIndex: currentIndex),
      bottomNavigationBar: const BottomNavBarWidget(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Navigate to create new shipment screen
        },
        backgroundColor: ColorManager.primaryColor,
        child: Icon(
          Icons.add,
          color: Colors.white,
          size: 30.w,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  switch (currentIndex) {
    case 0:
      return 'الصفحة الرئيسية';
    case 1:
      return 'سجل الشحنات';
    case 2:
      return 'المحفظة';
    case 3:
      return 'المنيو';
  }

  return 'الصفحة الرئيسية';
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomeScreen();
      case 1:
        return _buildComingSoonScreen('سجل الشحنات');
      case 2:
        return _buildComingSoonScreen('المحفظة');
      case 3:
        return _buildComingSoonScreen('المنيو');
    }
    return const SizedBox.shrink();
  }

  Widget _buildComingSoonScreen(String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              'قريباً...',
              style: AppTextStyles.headlineSmall.copyWith(
                color: ColorManager.greyText,
              ),
            ),
            AppGaps.gap8,
            Text(
              'هذه الصفحة قيد التطوير',
              style: AppTextStyles.bodyMedium.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
