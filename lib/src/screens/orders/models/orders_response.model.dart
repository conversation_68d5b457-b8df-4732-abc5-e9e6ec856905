import 'order.model.dart';

class OrdersResponseModel {
  final bool success;
  final String message;
  final OrdersDataModel data;

  OrdersResponseModel({
    required this.success,
    required this.message,
    required this.data,
  });

  factory OrdersResponseModel.fromJson(Map<String, dynamic> json) {
    return OrdersResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: OrdersDataModel.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class OrdersDataModel {
  final List<OrderModel> created;
  final List<OrderModel> received;

  OrdersDataModel({
    required this.created,
    required this.received,
  });

  factory OrdersDataModel.fromJson(Map<String, dynamic> json) {
    return OrdersDataModel(
      created: (json['created'] as List<dynamic>)
          .map((e) => OrderModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      received: (json['received'] as List<dynamic>)
          .map((e) => OrderModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'created': created.map((e) => e.toJson()).toList(),
      'received': received.map((e) => e.toJson()).toList(),
    };
  }

  OrdersDataModel copyWith({
    List<OrderModel>? created,
    List<OrderModel>? received,
  }) {
    return OrdersDataModel(
      created: created ?? this.created,
      received: received ?? this.received,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrdersDataModel &&
        other.created == created &&
        other.received == received;
  }

  @override
  int get hashCode {
    return Object.hash(created, received);
  }

  @override
  String toString() {
    return 'OrdersDataModel(created: $created, received: $received)';
  }
}
