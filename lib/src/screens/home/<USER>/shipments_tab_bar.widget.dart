import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/providers/order.providers.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:xr_helper/xr_helper.dart';

import 'shipment_card.widget.dart';

class ShipmentsTabBarWidget extends HookConsumerWidget {
  const ShipmentsTabBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabController = useTabController(initialLength: 2);
    final selectedTab = useState(0);
    final ordersAsync = ref.watch(getOrdersFutureProvider);

    // Listen to tab changes
    useEffect(() {
      void listener() {
        selectedTab.value = tabController.index;
      }

      tabController.addListener(listener);
      return () => tabController.removeListener(listener);
    }, [tabController]);

    return Column(
      children: [
        // Tab Bar
        Container(
          margin: EdgeInsets.symmetric(horizontal: 20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25.r),
            border: Border.all(color: ColorManager.lightGrey),
          ),
          child: TabBar(
            controller: tabController,
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(25.r),
              color: ColorManager.primaryColor,
            ),
            labelColor: Colors.white,
            unselectedLabelColor: ColorManager.greyText,
            labelStyle: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: AppTextStyles.bodyMedium,
            dividerColor: Colors.transparent,
            tabs: [
              Tab(text: context.tr.myReceivedShipments),
              Tab(text: context.tr.mySentShipments),
            ],
          ),
        ),

        AppGaps.gap16,

        // Tab Bar View
        Expanded(
          child: TabBarView(
            controller: tabController,
            children: [
              // Received Shipments Tab
              ordersAsync.when(
                data: (ordersResponse) => _buildShipmentsList(
                  context,
                  ordersResponse.data.received,
                  context.tr.noReceivedShipments,
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text('${context.tr.dataLoadError}: $error'),
                ),
              ),
              // Sent Shipments Tab
              ordersAsync.when(
                data: (ordersResponse) => _buildShipmentsList(
                  context,
                  ordersResponse.data.created,
                  context.tr.noSentShipments,
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text('${context.tr.dataLoadError}: $error'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShipmentsList(
    BuildContext context,
    List<OrderModel> orders,
    String emptyMessage,
  ) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              emptyMessage,
              style: AppTextStyles.bodyLarge.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      );
    }

    return BaseList<OrderModel>(
      data: orders,
      padding: EdgeInsets.only(bottom: 100.h),
      itemBuilder: (order, index) => ShipmentCardWidget(
        order: order,
        onTap: () {
          // TODO: Navigate to shipment details
        },
      ),
    );
  }
}
