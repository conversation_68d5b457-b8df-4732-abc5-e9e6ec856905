import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../widgets/shipments_tab_bar.widget.dart';

class HomeScreen extends HookConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          context.tr.ahmadAli,
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.only(
            right: AppSpaces.padding8,
            bottom: AppSpaces.padding8,
            top: AppSpaces.padding8,
          ),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: Column(
        children: [
          // Active Shipments Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.screenPadding,
              vertical: 16.h,
            ),
            child: Text(
              context.tr.activeShipments,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Shipments TabBar Widget
          const Expanded(
            child: ShipmentsTabBarWidget(),
          ),
        ],
      ),
    );
  }
}
