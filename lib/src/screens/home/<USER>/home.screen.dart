import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/providers/order.providers.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../widgets/shipment_card.widget.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersAsync = ref.watch(getOrdersFutureProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          'أحمد علي',
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.only(
            right: AppSpaces.padding8,
            bottom: AppSpaces.padding8,
            top: AppSpaces.padding8,
          ),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: Column(
        children: [
          // Active Shipments Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpaces.screenPadding,
              vertical: 16.h,
            ),
            child: Text(
              'الشحنات النشطة',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Tab Bar
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.r),
              border: Border.all(color: ColorManager.lightGrey),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(25.r),
                color: ColorManager.primaryColor,
              ),
              labelColor: Colors.white,
              unselectedLabelColor: ColorManager.greyText,
              labelStyle: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: AppTextStyles.bodyMedium,
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(text: 'شحناتي المستلمة'),
                Tab(text: 'شحناتي المرسلة'),
              ],
            ),
          ),

          AppGaps.gap16,

          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Received Shipments Tab
                ordersAsync.when(
                  data: (ordersResponse) => _buildShipmentsList(
                    ordersResponse.data.received,
                    'لا توجد شحنات مستلمة',
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('خطأ في تحميل البيانات: $error'),
                  ),
                ),
                // Sent Shipments Tab
                ordersAsync.when(
                  data: (ordersResponse) => _buildShipmentsList(
                    ordersResponse.data.created,
                    'لا توجد شحنات مرسلة',
                  ),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('خطأ في تحميل البيانات: $error'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentsList(List<OrderModel> orders, String emptyMessage) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              emptyMessage,
              style: AppTextStyles.bodyLarge.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      );
    }

    return BaseList<OrderModel>(
      data: orders,
      padding: EdgeInsets.only(bottom: 100.h),
      itemBuilder: (order, index) => ShipmentCardWidget(
        order: order,
        onTap: () {
          // TODO: Navigate to shipment details
        },
      ),
    );
  }
}
