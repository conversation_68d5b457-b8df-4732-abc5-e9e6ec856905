import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final iconSize = 25.w;

    final index0 = currentIndex == 0;
    final index1 = currentIndex == 1;
    final index2 = currentIndex == 2;
    final index3 = currentIndex == 3;

    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.w),
          topRight: Radius.circular(25.w),
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          unselectedItemColor: ColorManager.black,
          selectedItemColor: ColorManager.primaryColor,
          selectedLabelStyle: AppTextStyles.labelMedium,
          unselectedLabelStyle: AppTextStyles.labelLarge,
          elevation: 1,
          backgroundColor: Colors.white,
          currentIndex: currentIndex,
          onTap: (index) {
            bottomNavCtrl.changeIndex(index);
          },
          items: [
            BottomNavigationBarItem(
              icon: Icon(
                CupertinoIcons.house_fill,
                size: iconSize,
                color:
                    index0 ? ColorManager.primaryColor : ColorManager.greyIcon,
              ),
              label: context.tr.home,
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.video_collection,
                size: iconSize,
                color:
                    index1 ? ColorManager.primaryColor : ColorManager.greyIcon,
              ),
              label: context.tr.reels,
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.medical_services,
                size: iconSize,
                color:
                    index2 ? ColorManager.primaryColor : ColorManager.greyIcon,
              ),
              label: context.tr.doctors,
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.store,
                size: iconSize,
                color:
                    index3 ? ColorManager.primaryColor : ColorManager.greyIcon,
              ),
              label: context.tr.shops,
            ),
          ],
        ),
      ),
    );
  }
}
