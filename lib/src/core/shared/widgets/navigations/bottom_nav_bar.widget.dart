import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final iconSize = 24.w;

    return Container(
      height: 80.h,
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25.r),
        border: Border.all(
          color: ColorManager.primaryColor,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(
            icon: CupertinoIcons.house_fill,
            label: 'الصفحة الرئيسية',
            index: 0,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(0),
          ),
          _buildNavItem(
            icon: Icons.history,
            label: 'سجل الشحنات',
            index: 1,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(1),
          ),
          // Floating Action Button Space
          SizedBox(width: 60.w),
          _buildNavItem(
            icon: Icons.account_balance_wallet,
            label: 'المحفظة',
            index: 2,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(2),
          ),
          _buildNavItem(
            icon: Icons.menu,
            label: 'المنيو',
            index: 3,
            currentIndex: currentIndex,
            iconSize: iconSize,
            onTap: () => bottomNavCtrl.changeIndex(3),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required int currentIndex,
    required double iconSize,
    required VoidCallback onTap,
  }) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 16.w : 8.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          color: isSelected ? ColorManager.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: isSelected ? Colors.white : ColorManager.greyIcon,
            ),
            if (isSelected) ...[
              AppGaps.gap8,
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
